#!/usr/bin/env python3
"""
Simple test script for the UnknownFacebankManager functionality.
"""

import os
import sys
from pathlib import Path

print("🚀 Starting Simple Unknown Facebank Manager Test")
print("=" * 50)

try:
    # Add the demo_server directory to Python path
    demo_server_path = Path(__file__).parent / "demo_server"
    sys.path.insert(0, str(demo_server_path))
    print(f"✅ Added path: {demo_server_path}")

    # Setup Django
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'web_server.settings')
    print("✅ Set Django settings module")

    import django
    django.setup()
    print("✅ Django setup complete")

    # Test import
    from recognition.unknown_facebank_manager import UnknownFacebankManager
    print("✅ Successfully imported UnknownFacebankManager")

    # Test basic initialization without model components
    print("\n🔧 Testing basic initialization...")
    unknown_manager = UnknownFacebankManager()
    print("✅ UnknownFacebankManager initialized successfully")

    # Test getting next unknown ID
    print("\n🔢 Testing ID generation...")
    next_id = unknown_manager.get_next_unknown_id()
    print(f"✅ Next unknown ID: {next_id}")

    # Test getting facebank info
    print("\n📊 Testing facebank info...")
    info = unknown_manager.get_facebank_info()
    print(f"✅ Total persons: {info['total_persons']}")
    print(f"✅ Person names: {info['person_names']}")
    print(f"✅ Embeddings shape: {info['embeddings_shape']}")
    print(f"✅ Facebank path: {info['facebank_path']}")
    print(f"✅ Names path: {info['names_path']}")

    print("\n🎉 Simple test completed successfully!")

except Exception as e:
    print(f"\n❌ Error during testing: {str(e)}")
    import traceback
    traceback.print_exc()
