#!/usr/bin/env python3
"""
Minimal test to verify unknown facebank manager functionality
"""

import os
import sys
import django
import logging
from pathlib import Path

# Add the demo_server directory to Python path
demo_server_path = Path(__file__).parent
sys.path.insert(0, str(demo_server_path))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'web_server.settings')
django.setup()

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# Write test results to file
test_results = []

try:
    test_results.append("🧪 Starting minimal test")
    
    from recognition.unknown_facebank_manager import UnknownFacebankManager
    test_results.append("✅ Successfully imported UnknownFacebankManager")
    
    # Test basic initialization
    unknown_manager = UnknownFacebankManager()
    test_results.append("✅ Successfully initialized UnknownFacebankManager")
    
    # Test getting next ID
    next_id = unknown_manager.get_next_unknown_id()
    test_results.append(f"✅ Next ID: {next_id}")
    
    # Test face bank info
    info = unknown_manager.get_facebank_info()
    test_results.append(f"✅ Face bank info:")
    test_results.append(f"   Total persons: {info['total_persons']}")
    test_results.append(f"   Person names: {info['person_names']}")
    test_results.append(f"   Facebank exists: {info['facebank_exists']}")
    test_results.append(f"   Names exists: {info['names_exists']}")
    
    # Test validation
    validation = unknown_manager.validate_facebank_integrity()
    test_results.append(f"✅ Validation:")
    test_results.append(f"   Is valid: {validation['is_valid']}")
    if validation['issues']:
        test_results.append(f"   Issues: {validation['issues']}")
    if validation['warnings']:
        test_results.append(f"   Warnings: {validation['warnings']}")
    
    test_results.append("🎉 All tests completed successfully!")
    
except Exception as e:
    test_results.append(f"❌ Test failed: {str(e)}")
    import traceback
    test_results.append(traceback.format_exc())

# Write results to file
with open('test_results.txt', 'w') as f:
    for result in test_results:
        f.write(result + '\n')
        print(result)

print(f"\n📝 Test results written to test_results.txt")
