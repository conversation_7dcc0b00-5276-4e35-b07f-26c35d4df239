"""
Simple validation script for the new service layer
"""
import os
import sys
import django

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'web_server.settings')
django.setup()

import numpy as np
from videostream.services.recognition_service import RecognitionService
from videostream.services.zone_service import ZoneService
from videostream.services.drawing_service import DrawingService


def test_services():
    """Test basic service functionality"""
    print("🧪 Testing Service Layer...")
    
    # Test 1: Recognition Service
    print("1. Testing RecognitionService...")
    try:
        recognizer = RecognitionService.get_recognizer(camera_id=1)
        if recognizer:
            print("   ✅ RecognitionService initialized successfully")
        else:
            print("   ⚠️  RecognitionService returned None (expected if no model)")
    except Exception as e:
        print(f"   ❌ RecognitionService error: {str(e)}")
    
    # Test 2: Zone Service
    print("2. Testing ZoneService...")
    try:
        # Test zone filtering with dummy data
        bboxes = np.array([[100, 100, 200, 200], [300, 300, 400, 400]])
        names = ["Person1", "Person2"]
        confidences = [0.9, 0.8]
        zones = [{"points": [[50, 50], [250, 50], [250, 250], [50, 250]]}]
        frame_shape = (480, 640, 3)
        
        filtered_bboxes, filtered_names, filtered_confidences = ZoneService.filter_detections_by_zones(
            bboxes, names, confidences, zones, frame_shape
        )
        print(f"   ✅ ZoneService filtered {len(bboxes)} -> {len(filtered_bboxes)} detections")
    except Exception as e:
        print(f"   ❌ ZoneService error: {str(e)}")
    
    # Test 3: Drawing Service
    print("3. Testing DrawingService...")
    try:
        # Create dummy frame
        frame = np.zeros((480, 640, 3), dtype=np.uint8)
        bboxes = np.array([[100, 100, 200, 200]])
        names = ["TestPerson"]
        confidences = [0.95]
        
        annotated_frame = DrawingService.annotate_frame(frame, bboxes, names, confidences)
        if annotated_frame.shape == frame.shape:
            print("   ✅ DrawingService annotation successful")
        else:
            print("   ❌ DrawingService returned wrong frame shape")
    except Exception as e:
        print(f"   ❌ DrawingService error: {str(e)}")
    
    print("🎯 Service layer validation complete!")


if __name__ == "__main__":
    test_services()
