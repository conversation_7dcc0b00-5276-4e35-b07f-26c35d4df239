import numpy as np
from PIL import Image
from django.core.files.base import ContentFile
from io import BytesIO
from django.utils import timezone

from django.forms import formset_factory
from django.http import HttpResponse, HttpResponseForbidden, JsonResponse
from django.contrib.auth.models import User
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login, authenticate, logout
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.urls import reverse
from django.contrib.auth.views import LoginView
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.conf import settings
from django.contrib.auth import logout as django_logout
from django.http import HttpResponseRedirect
from urllib.parse import urlencode

from alerts.utils import detect_faces
from .forms import is_admin_user
from datetime import datetime

from .models import UserProfile
from .forms import RegisterForm, UserProfileForm, UserUpdateForm
from .middleware import require_permission
from recognition.facenet_recognizer import FaceNetRecognizer

import logging
logger = logging.getLogger(__name__)


def index(request):
    return HttpResponse("Users index.")


@login_required
def user_info(request):
    """Display current user information and permissions"""
    user_profile = None
    permissions = []
    realm_roles = []
    client_roles = []
    
    if hasattr(request.user, 'userprofile'):
        user_profile = request.user.userprofile
        permissions = user_profile.get_permissions()
        realm_roles = user_profile.get_keycloak_realm_roles()
        client_roles = user_profile.get_keycloak_client_roles()
    
    context = {
        'user_profile': user_profile,
        'permissions': permissions,
        'realm_roles': realm_roles,
        'client_roles': client_roles,
        'is_keycloak_user': user_profile and user_profile.keycloak_id,
    }
    
    return render(request, 'users/user_info.html', context)


@require_http_methods(["POST"])
@csrf_exempt
def sync_user_roles(request):
    """API endpoint to manually sync user roles from Keycloak"""
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Authentication required'}, status=401)
    
    if not hasattr(request.user, 'userprofile') or not request.user.userprofile.keycloak_id:
        return JsonResponse({'error': 'User is not managed by Keycloak'}, status=400)
    
    try:
        from .auth_backends import KeycloakOIDCAuthenticationBackend
        
        backend = KeycloakOIDCAuthenticationBackend()
        
        # Fetch current roles from Keycloak
        roles_dict = backend.fetch_user_roles_from_keycloak(request.user.userprofile.keycloak_id)
        
        # Create fake claims dict for update_user method
        fake_claims = {
            'sub': request.user.userprofile.keycloak_id,
            'email': request.user.email,
            'given_name': request.user.first_name,
            'family_name': request.user.last_name,
        }
        
        # Update user with new roles
        backend.update_user(request.user, fake_claims)
        
        return JsonResponse({
            'success': True,
            'realm_roles': roles_dict.get('realm_roles', []),
            'client_roles': roles_dict.get('client_roles', []),
            'permissions': request.user.userprofile.get_permissions()
        })
    
    except Exception as e:
        logger.error(f"Error syncing user roles: {str(e)}")
        return JsonResponse({'error': 'Failed to sync roles'}, status=500)


def keycloak_logout_callback(request):
    """Handle logout callback from Keycloak"""
    messages.success(request, 'You have been successfully logged out.')
    return redirect('index')

def keycloak_logout(request):
    # 1) id_token'ı session'dan al (Logout'tan önce!)
    id_token = request.session.get('oidc_id_token')

    # 2) Önce Django oturumunu kapat
    django_logout(request)

    # 3) Keycloak end-session URL'ini hazırla
    #    post_logout_redirect_uri: Keycloak'tan döndüğünde gideceğin URL
    post_logout_redirect_uri = request.build_absolute_uri(reverse('users:keycloak_logout_callback'))

    # Keycloak standard end-session endpoint:
    end_session_url = (
        f"{settings.KEYCLOAK_SERVER_URL}/realms/{settings.KEYCLOAK_REALM}/protocol/openid-connect/logout"
    )

    params = {
        'post_logout_redirect_uri': post_logout_redirect_uri,
    }

    # id_token varsa Keycloak'a ipucu olarak gönder (tavsiye edilir)
    if id_token:
        params['id_token_hint'] = id_token

    return HttpResponseRedirect(f"{end_session_url}?{urlencode(params)}")