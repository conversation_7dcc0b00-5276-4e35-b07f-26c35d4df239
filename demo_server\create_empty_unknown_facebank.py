#!/usr/bin/env python3
"""
Create empty unknown face bank files manually
"""

import torch
import numpy as np
import os

# Create the unknowns directory if it doesn't exist
unknowns_dir = "./media/alert_photos/unknowns"
os.makedirs(unknowns_dir, exist_ok=True)

# Create empty face bank files
unknown_facebank_path = os.path.join(unknowns_dir, "unknown_facebank.pth")
unknown_names_path = os.path.join(unknowns_dir, "unknown_names.npy")

# Create empty embeddings (None)
torch.save(None, unknown_facebank_path)
print(f"Created empty facebank: {unknown_facebank_path}")

# Create empty names array
empty_names = np.array([])
np.save(unknown_names_path, empty_names)
print(f"Created empty names: {unknown_names_path}")

print("✅ Empty unknown face bank files created successfully!")

# Verify files exist
print(f"Facebank exists: {os.path.exists(unknown_facebank_path)}")
print(f"Names exists: {os.path.exists(unknown_names_path)}")

# Test loading
try:
    loaded_embeddings = torch.load(unknown_facebank_path, weights_only=True)
    loaded_names = np.load(unknown_names_path, allow_pickle=True)
    print(f"✅ Successfully loaded files")
    print(f"   Embeddings: {loaded_embeddings}")
    print(f"   Names: {loaded_names} (length: {len(loaded_names)})")
except Exception as e:
    print(f"❌ Error loading files: {e}")
