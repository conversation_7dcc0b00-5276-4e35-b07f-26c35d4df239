"""
Django settings for web_server project.

Generated by 'django-admin startproject' using Django 5.1.

For more information on this file, see
https://docs.djangoproject.com/en/5.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.1/ref/settings/
"""

from pathlib import Path
import os
from dotenv import load_dotenv

# Import multiprocessing utilities for CUDA compatibility
# This must be done before any other imports that might use multiprocessing
from .mp_utils import setup_multiprocessing

# Load environment variables from .env file
load_dotenv()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.getenv('SECRET_KEY', "django-insecure-b0h5=+7&8ig^lzx$c*01qp7t@r4rxxh68la91ancfvdhax_=6w")

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = os.getenv('DEBUG', 'True').lower() in ['true', '1', 'yes']

ALLOWED_HOSTS = []


# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "users",
    "cameras",
    "alerts",
    "rest_framework",  
    "django_filters",
    'recognition',
    'videostream',  
    'channels',
    'mozilla_django_oidc',  # Keycloak OIDC integration

]

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "users.middleware.KeycloakRoleMiddleware",  # Custom role-based access middleware
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

ROOT_URLCONF = "web_server.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": ["templates"],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
                'social_django.context_processors.backends',
                'social_django.context_processors.login_redirect',
            ],
        },
    },
]

WSGI_APPLICATION = "web_server.wsgi.application"


# Database
# https://docs.djangoproject.com/en/5.1/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.sqlite3",
        "NAME": BASE_DIR / "db.sqlite3",
    }
}


# Password validation
# https://docs.djangoproject.com/en/5.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.1/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.1/howto/static-files/

STATIC_URL = "/static/"
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, 'static'),
]

# Default primary key field type
# https://docs.djangoproject.com/en/5.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# Add this at the end of the file
LOGIN_URL = 'login'
LOGIN_REDIRECT_URL = 'index'
LOGOUT_REDIRECT_URL = 'index'

# Add these lines at the end of the file
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# Add these lines at the end of the file
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'  # For development


# Add these settings for REST Framework
REST_FRAMEWORK = {
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 10,
    'DEFAULT_FILTER_BACKENDS': ['django_filters.rest_framework.DjangoFilterBackend'],
}

RECOGNITION_CONFIG = {
    'model_path': '../model/',
    'facebank_path': './media/alert_photos',
    'device': 'cuda',  # or 'cuda' if you're using GPU
    'use_mobilfacenet': False,  # Set to True if you want to use MobileFaceNet
    'embedding_size': 512,
    'net_depth': 50,
    'drop_ratio': 0.6,
    'net_mode': 'ir_se',
    'face_limit': 30,  # Maximum number of faces to detect in a single image
    'min_face_size': 30.0,  # Minimum face size to detect
    'test_transform': None,  # You might want to define this in your recognizer.py
}

# If you're using torchvision transforms, you might want to define them here
# and import them in your recognizer.py
from torchvision import transforms

RECOGNITION_CONFIG['test_transform'] = transforms.Compose([
    transforms.ToTensor(),
    transforms.Normalize([0.5, 0.5, 0.5], [0.5, 0.5, 0.5])
])


from .celery import app as celery_app

ASGI_APPLICATION = "web_server.asgi.application"

# Celery settings
CELERY_BROKER_URL = 'redis://localhost:6379'  # Replace with your Redis broker URL
CELERY_RESULT_BACKEND = 'redis://localhost:6379'  # Replace with your Redis backend URL
CELERY_ACCEPT_CONTENT = ['application/json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = 'UTC'
CELERY_BEAT_SCHEDULE = {
    'check_camera_health': {
        'task': 'cameras.tasks.check_camera_health',
        'schedule': 30.0,  # Run every 30 seconds
    },
    'check_guest_access_expiration': {
        'task': 'cameras.tasks.check_guest_access_expiration',
        'schedule': 86400.0,  # Run every 24 hours (86400 seconds)
    },
}

# Add channel layers configuration
CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels.layers.InMemoryChannelLayer"
    }
}

AUTHENTICATION_BACKENDS = (
    'users.auth_backends.KeycloakOIDCAuthenticationBackend',  # Custom Keycloak backend
    'django.contrib.auth.backends.ModelBackend',  # Fallback for admin users
    'mozilla_django_oidc.auth.OIDCAuthenticationBackend',  # optional, if using OIDC
)

# Remove the Google OAuth2 settings

STREAM_COMPRESSION = "h264"  # or "mjpeg" based on your camera capabilities
OPENCV_FFMPEG_CAPTURE_OPTIONS = ";".join([
    "rtsp_transport", "udp",        # UDP or TCP based on your preference
    "stimeout", "5000000",           # 5s socket timeout (µs)
    "max_delay", "0"                 # no additional delay
])

# Keycloak OIDC Configuration
KEYCLOAK_CLIENT_ID = os.getenv('KEYCLOAK_CLIENT_ID')
KEYCLOAK_CLIENT_SECRET = os.getenv('KEYCLOAK_CLIENT_SECRET')
KEYCLOAK_SERVER_URL = os.getenv('KEYCLOAK_SERVER_URL', 'http://127.0.0.1:8080')
KEYCLOAK_REALM = os.getenv('KEYCLOAK_REALM', 'mymaster')

# Validate required Keycloak settings
if not KEYCLOAK_CLIENT_ID:
    raise ValueError('KEYCLOAK_CLIENT_ID environment variable is required')
if not KEYCLOAK_CLIENT_SECRET:
    raise ValueError('KEYCLOAK_CLIENT_SECRET environment variable is required')

# OIDC Configuration
OIDC_RP_CLIENT_ID = KEYCLOAK_CLIENT_ID
OIDC_RP_CLIENT_SECRET = KEYCLOAK_CLIENT_SECRET
OIDC_OP_AUTHORIZATION_ENDPOINT = f"{KEYCLOAK_SERVER_URL}/realms/{KEYCLOAK_REALM}/protocol/openid-connect/auth"
OIDC_OP_TOKEN_ENDPOINT = f"{KEYCLOAK_SERVER_URL}/realms/{KEYCLOAK_REALM}/protocol/openid-connect/token"
OIDC_OP_USER_ENDPOINT = f"{KEYCLOAK_SERVER_URL}/realms/{KEYCLOAK_REALM}/protocol/openid-connect/userinfo"
OIDC_OP_JWKS_ENDPOINT = f"{KEYCLOAK_SERVER_URL}/realms/{KEYCLOAK_REALM}/protocol/openid-connect/certs"
OIDC_RP_SIGN_ALGO = "RS256"
OIDC_RP_SCOPES = "openid profile email"
OIDC_STORE_ID_TOKEN = True

# Authentication and authorization settings
LOGIN_REDIRECT_URL = '/'
LOGOUT_REDIRECT_URL = '/'
OIDC_CREATE_USER = True
OIDC_UPDATE_USER = True

# Role mapping configuration
KEYCLOAK_REALM_ROLES = {
    'django-admin': {
        'is_staff': True,
        'is_superuser': True,
        'permissions': ['all']
    },
    'django-staff': {
        'is_staff': True,
        'is_superuser': False,
        'permissions': []  # Permissions come from client roles
    }
}

# Client role to permission mapping
KEYCLOAK_CLIENT_ROLES = {
    'camera-access': ['cameras'],
    'person-access': ['persons']
}
