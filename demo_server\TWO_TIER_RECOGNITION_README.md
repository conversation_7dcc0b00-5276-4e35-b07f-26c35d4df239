# Two-Tier Face Recognition System

## Overview

This implementation integrates the unknown person face bank management system into the existing FaceNet recognizer, creating a comprehensive two-tier recognition system that prevents rapid ID incrementation while maintaining proper tracking of unknown individuals.

## Recognition Flow

### Step-by-Step Process

1. **Face Detection**: Detect faces in the input frame using MTCNN
2. **Embedding Generation**: Generate face embeddings for all detected faces
3. **Primary Face Bank Check**: Check against main face bank (known persons)
   - If similarity score is above threshold → return matched known person's name
   - If similarity score is below threshold → proceed to step 4
4. **Unknown Face Bank Check**: Check against unknown persons face bank
   - Calculate similarity scores against all existing unknown person embeddings
   - If highest similarity score is above unknown threshold → return existing Unknown_ID
   - If no match found above threshold → proceed to step 5
5. **Create New Unknown Person**: When no matches found in either face bank
   - Generate new Unknown_ID using auto-increment system
   - Add face embedding to unknown face bank
   - Save face image to unknown person directory
   - Return new Unknown_ID

## Key Features

### ✅ **Implemented Features**

- **Two-Tier Recognition**: Separate checking for known and unknown persons
- **Automatic Unknown Person Management**: Creates and tracks unknown persons consistently
- **Configurable Thresholds**: Separate thresholds for known and unknown person matching
- **Backward Compatibility**: Existing recognition calls continue to work unchanged
- **Comprehensive Logging**: Detailed logging for debugging and monitoring
- **Error Handling**: Robust error handling throughout the recognition pipeline

### 🔧 **Configuration Options**

All threshold values are centrally managed in `settings.py`:

```python
RECOGNITION_CONFIG = {
    # ... existing config ...
    'known_threshold': 1.35,  # Threshold for known persons (main face bank)
    'unknown_threshold': 1.55,  # Threshold for unknown face bank matching
    'enable_unknown_facebank': True,  # Enable/disable unknown face bank
}
```

**Important**: All threshold values are read from this single configuration source. No hardcoded values exist in the code.

## Integration Details

### Modified Components

1. **FaceNetRecognizer Class** (`demo_server/recognition/facenet_recognizer.py`)
   - Added unknown face bank manager initialization
   - Completely rewritten `recognize()` method with two-tier logic
   - Added helper methods for face bank checking and unknown person creation

2. **Configuration** (`demo_server/web_server/settings.py`)
   - Added unknown face bank configuration options
   - Configurable thresholds for fine-tuning recognition behavior

### New Methods Added

#### Core Recognition Methods
- `_generate_face_embeddings()` - Generate embeddings for detected faces
- `_check_main_facebank()` - Check against known persons face bank
- `_check_unknown_facebank_single()` - Check against unknown persons face bank
- `_create_new_unknown_person()` - Create new unknown person entry

#### Utility Methods
- `_check_unknown_facebank()` - Batch checking for unknown face bank
- Various helper methods for embedding processing and similarity calculation

## Usage Examples

### Basic Recognition (Unchanged Interface)
```python
from recognition.facenet_recognizer import FaceNetRecognizer

# Initialize recognizer (now includes unknown face bank)
recognizer = FaceNetRecognizer()

# Perform recognition (same interface as before)
bboxes, names, confidences = recognizer.recognize(frame, camera_id="camera_1")

# Results now include:
# - Known person names (e.g., "John", "Alice")
# - Existing unknown person IDs (e.g., "Unknown_3", "Unknown_7")
# - New unknown person IDs (automatically created)
```

### Advanced Usage with Configuration
```python
# Custom threshold for stricter unknown matching
bboxes, names, confidences = recognizer.recognize(
    frame, 
    threshold=1.5,  # Higher threshold = more strict for known persons
    camera_id="camera_1"
)
```

## Expected Behavior

### Known Persons
- **Existing Behavior**: Continue to work exactly as before
- **Recognition**: Matched against main face bank first
- **Confidence**: High confidence scores for good matches

### Unknown Persons
- **First Detection**: Creates new Unknown_ID (e.g., "Unknown_8")
- **Subsequent Detections**: Reuses existing Unknown_ID if face is similar enough
- **Consistency**: Same unknown person gets same ID across multiple detections
- **No ID Inflation**: Prevents rapid incrementation of unknown person IDs

### New Unknown Person Creation
- **Automatic**: Happens when no matches found in either face bank
- **Directory Creation**: Creates directory structure for unknown person
- **Face Storage**: Saves face image for future reference
- **Database Integration**: Can be extended to save to database

## Performance Considerations

### Optimizations Implemented
- **Batch Processing**: Efficient embedding generation for multiple faces
- **Shared Models**: Unknown face bank uses same MTCNN and FaceNet models
- **Memory Management**: Proper tensor handling and cleanup
- **Caching**: Face bank data cached in memory for fast access

### Performance Impact
- **Minimal Overhead**: Two-tier checking adds minimal processing time
- **Scalable**: Handles large numbers of unknown persons efficiently
- **Memory Efficient**: Shared model references reduce memory usage

## Testing

### Test Scripts Available
1. **`test_two_tier_recognition.py`** - Comprehensive integration testing
2. **`test_unknown_facebank_manager.py`** - Unknown face bank functionality testing
3. **`unknown_integration_example.py`** - Integration examples and demos

### Running Tests
```bash
cd demo_server

# Test the complete two-tier system
python test_two_tier_recognition.py

# Test unknown face bank functionality
python test_unknown_facebank_manager.py

# Run integration examples
python unknown_integration_example.py
```

## Configuration Tuning

### Centralized Threshold Management

All threshold values are managed from a single location in `demo_server/web_server/settings.py`. To change thresholds:

1. **Edit settings.py**:
```python
RECOGNITION_CONFIG = {
    # ... other settings ...
    'known_threshold': 1.35,    # Change this for known persons
    'unknown_threshold': 1.55,  # Change this for unknown persons
}
```

2. **Restart the application** for changes to take effect.

### Threshold Recommendations

#### Main Face Bank Threshold (Known Persons)
- **Current**: 1.35 (optimized for known person recognition)
- **Stricter**: 1.2 (fewer false positives, more strict matching)
- **Looser**: 1.5 (more matches, potential false positives)

#### Unknown Face Bank Threshold
- **Current**: 1.55 (optimized for unknown person grouping)
- **Stricter**: 1.3 (creates more unique unknown persons)
- **Looser**: 1.7 (groups more faces as same unknown person)

### Performance Tuning
```python
RECOGNITION_CONFIG = {
    # For high accuracy environments (more strict)
    'known_threshold': 1.2,
    'unknown_threshold': 1.3,

    # For high throughput environments (more permissive)
    'known_threshold': 1.5,
    'unknown_threshold': 1.7,
}
```

## Troubleshooting

### Common Issues

1. **Unknown Persons Not Being Created**
   - Check `enable_unknown_facebank` setting
   - Verify unknown face bank manager initialization
   - Check file permissions for unknowns directory

2. **Too Many Unknown Persons Created**
   - Lower `unknown_threshold` value
   - Increase `unknown_similarity_threshold`
   - Check face quality and alignment

3. **Known Persons Misidentified as Unknown**
   - Check main face bank threshold
   - Verify main face bank embeddings
   - Review face bank preparation

### Debug Information
```python
# Enable detailed logging
import logging
logging.getLogger('recognition.facenet_recognizer').setLevel(logging.DEBUG)
logging.getLogger('recognition.unknown_facebank_manager').setLevel(logging.DEBUG)
```

## Future Enhancements

### Potential Improvements
1. **Face Quality Assessment**: Only save high-quality face images
2. **Clustering**: Group similar unknown faces automatically
3. **Database Integration**: Store unknown person metadata in database
4. **Face Similarity Visualization**: Tools to visualize face similarities
5. **Automatic Cleanup**: Periodic cleanup of old unknown persons

## Support

For issues with the two-tier recognition system:
1. Check log files for error messages
2. Run test scripts to verify functionality
3. Review configuration settings
4. Validate face bank integrity using provided tools
