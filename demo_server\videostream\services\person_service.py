import logging
from django.db import transaction
from django.core.files.base import ContentFile
from django.utils import timezone
from asgiref.sync import sync_to_async
import cv2

# Import models
from alerts.models import <PERSON><PERSON><PERSON><PERSON>, AlertPhoto

logger = logging.getLogger(__name__)


class PersonService:
    """Service for handling person-related operations"""

    def __init__(self):
        self.unknown_count = self._get_latest_unknown_count()
        logger.info(f"PersonService initialized with unknown_count: {self.unknown_count}")

    def _get_latest_unknown_count(self):
        """
        Get the latest unknown count from existing directories
        Following original working logic from git history
        """
        try:
            from django.conf import settings
            import os

            # Check unknowns directory like original logic
            base_dir = settings.MEDIA_ROOT
            unknown_dir = os.path.join(base_dir, 'alert_photos', 'unknowns')

            if not os.path.exists(unknown_dir):
                logger.info(f"Unknown directory doesn't exist, starting from 0")
                return 0

            max_number = 0

            # Scan existing Unknown_X directories
            for item in os.listdir(unknown_dir):
                item_path = os.path.join(unknown_dir, item)
                if os.path.isdir(item_path) and item.startswith("Unknown_"):
                    try:
                        number_part = item.split("_")[1]
                        number = int(number_part)
                        max_number = max(max_number, number)
                    except (ValueError, IndexError):
                        logger.warning(f"Malformed unknown directory name: {item}")
                        continue

            logger.info(f"Found {max_number} existing unknown persons in directories")
            return max_number

        except Exception as e:
            logger.error(f"Error getting latest unknown count: {str(e)}")
            return 0
    
    async def process_unknown_person(self, frame, camera):
        """
        Process and save unknown person following original working logic
        Simple approach: increment counter, create directory, save to database

        Args:
            frame: Full video frame
            camera: Camera object

        Returns:
            tuple: (AlertPerson instance, person_name) or (None, None)
        """
        try:
            # Increment counter and generate name (like original logic)
            self.unknown_count += 1
            person_name = f"Unknown_{self.unknown_count}"

            logger.info(f"🔄 Processing unknown person: {person_name}")

            # Create unknown person synchronously (simpler approach)
            alert_person, alert_photo = await sync_to_async(self._handle_unknown_person_sync)(
                person_name, camera, frame
            )

            if alert_person and alert_photo:
                logger.info(f"✅ Unknown person created successfully: {person_name}")
                logger.info(f"   - Database ID: {alert_person.id}")
                logger.info(f"   - Directory created for: {person_name}")
                return alert_person, person_name
            else:
                logger.error(f"❌ Failed to create unknown person: {person_name}")
                # Decrement counter on failure
                self.unknown_count = max(0, self.unknown_count - 1)
                return None, None

        except Exception as e:
            logger.error(f"❌ Error processing unknown person: {str(e)}")
            # Decrement counter on failure
            self.unknown_count = max(0, self.unknown_count - 1)
            return None, None

    def _handle_unknown_person_sync(self, person_name, camera, frame):
        """
        Handle unknown person following original working logic
        Simple and reliable approach like the original implementation

        Args:
            person_name: Generated person name (Unknown_X)
            camera: Camera object
            frame: Video frame

        Returns:
            tuple: (AlertPerson, AlertPhoto) or (None, None)
        """
        try:
            from django.conf import settings
            import os

            # Create directory structure (original logic)
            base_dir = settings.MEDIA_ROOT
            unknown_dir = os.path.join(base_dir, 'alert_photos', 'unknowns')
            person_dir = os.path.join(unknown_dir, person_name)

            # Create directories
            os.makedirs(person_dir, exist_ok=True)
            logger.debug(f"📁 Created directory: {person_dir}")

            # Save face image to filesystem (original logic)
            face_filename = 'face.jpg'
            face_path = os.path.join(person_dir, face_filename)

            success = cv2.imwrite(face_path, frame)
            if not success:
                logger.error(f"❌ Failed to save face image to {face_path}")
                return None, None

            logger.debug(f"💾 Saved face image: {face_path}")

            # Create database records
            with transaction.atomic():
                # Create AlertPerson
                alert_person = AlertPerson.objects.create(
                    user=camera.user,
                    name=person_name,
                    is_unknown=True,
                    first_seen_camera=camera
                )

                # Create AlertPhoto
                _, buffer = cv2.imencode('.jpg', frame)
                content = ContentFile(buffer.tobytes())
                alert_photo = AlertPhoto.objects.create(
                    person=alert_person,
                    is_primary=True
                )
                alert_photo.photo.save(
                    f'{person_name}.jpg',
                    content,
                    save=True
                )

                logger.info(f"✅ Unknown person saved successfully:")
                logger.info(f"   - Name: {person_name}")
                logger.info(f"   - Directory: {person_dir}")
                logger.info(f"   - Face file: {face_path}")
                logger.info(f"   - AlertPerson ID: {alert_person.id}")
                logger.info(f"   - AlertPhoto ID: {alert_photo.id}")

                return alert_person, alert_photo

        except Exception as e:
            logger.error(f"❌ Error handling unknown person {person_name}: {str(e)}")
            return None, None

    @staticmethod
    async def update_person_last_seen(person_name):
        """Update last seen date for a known person"""
        try:
            person = await sync_to_async(
                AlertPerson.objects.filter(name=person_name, is_unknown=False).first
            )()

            if person:
                from django.utils import timezone
                person.last_seen = timezone.now()
                await sync_to_async(person.save)()
                logger.debug(f"Updated last seen for {person_name}")
                return person
            else:
                logger.warning(f"Person not found: {person_name}")
                return None

        except Exception as e:
            logger.error(f"Error updating last seen for {person_name}: {str(e)}")
            return None
