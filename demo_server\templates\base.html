{% load static %}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Face Recognition System{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="{% static 'css/cameras.css' %}">
    <link rel="stylesheet" href="{% static 'css/alerts.css' %}">
    <link rel="stylesheet" href="{% static 'css/user.css' %}">
    <link rel="stylesheet" href="{% static 'css/index.css' %}">
</head>
<body class="bg-light" data-bs-theme="light">
<!-- Navigation -->
<nav class="navbar navbar-expand-lg navbar-dark bg-gradient shadow-sm sticky-top" style="padding: 1rem 0;">
    <div class="container">
        <a class="navbar-brand fw-bold d-flex align-items-center fs-4" href="{% url 'index' %}">
            <i class="bi bi-camera-fill me-2"></i>
            Face Recognition
        </a>
        <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link fs-6" href="{% url 'index' %}">
                        <i class="bi bi-house-fill me-1"></i>Home
                    </a>
                </li>
                {% if user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="cameraDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-camera-video-fill me-1"></i>Cameras
                        </a>
                        <ul class="dropdown-menu shadow">
                            <li><a class="dropdown-item" href="{% url 'cameras:index' %}">
                                <i class="bi bi-list-ul me-2"></i>Camera List
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'cameras:add' %}">
                                <i class="bi bi-plus-circle me-2"></i>Add Camera
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="alertDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-exclamation-triangle-fill me-1"></i>Persons
                        </a>
                        <ul class="dropdown-menu shadow">
                            <li><a class="dropdown-item" href="{% url 'alerts:index' %}">
                                <i class="bi bi-speedometer2 me-2"></i>Person Dashboard
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'alerts:person_list' %}">
                                <i class="bi bi-people-fill me-2"></i>View Persons
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'alerts:alert_list' %}">
                                <i class="bi bi-bell-fill me-2"></i>View Alerts
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'alerts:unknown_persons_list' %}">
                                <i class="bi bi-person-x-fill me-2"></i>Unknown Persons
                            </a></li>
                        </ul>
                    </li>
                {% endif %}
            </ul>
            <ul class="navbar-nav">
                <!-- Theme Toggle Button -->
                <li class="nav-item">
                    <button class="btn btn-outline-light btn-sm me-2" id="theme-toggle" title="Toggle Theme">
                        <i class="bi bi-sun-fill" id="theme-icon"></i>
                    </button>
                </li>
                {% if user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-person-circle me-1"></i>{{ user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end shadow">
                            <li>
                                <form method="post" action="{% url 'users:logout' %}" class="m-0">
                                    {% csrf_token %}
                                    <button type="submit" class="dropdown-item text-danger">
                                        <i class="bi bi-box-arrow-right me-2"></i>Logout
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </li>
                {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'oidc_authentication_init' %}">
                            <i class="bi bi-box-arrow-in-right me-1"></i>Login with Keycloak
                        </a>
                    </li>
                {% endif %}
            </ul>
        </div>
    </div>
</nav>
<!-- Toast Messages Container -->
<div class="toast-container position-fixed bottom-0 end-0 p-3" id="toast-container">
    {% if messages %}
        {% for message in messages %}
            <div class="toast show" role="alert" aria-live="assertive" aria-atomic="true" data-bs-autohide="true" data-bs-delay="5000">
                <div class="toast-header bg-{{ message.tags }} text-white border-0">
                    <i class="bi bi-{% if message.tags == 'success' %}check-circle-fill{% elif message.tags == 'error' %}exclamation-triangle-fill{% elif message.tags == 'warning' %}exclamation-circle-fill{% else %}info-circle-fill{% endif %} me-2"></i>
                    <strong class="me-auto">
                        {% if message.tags == 'success' %}Success
                        {% elif message.tags == 'error' %}Error  
                        {% elif message.tags == 'warning' %}Warning
                        {% else %}Info
                        {% endif %}
                    </strong>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body">
                    {{ message }}
                </div>
            </div>
        {% endfor %}
    {% endif %}
</div>

<!-- Main Content Container -->
<main class="container-fluid py-4">
    {% block content %}
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="text-center">
                    <h1 class="display-4 fw-bold text-primary mb-3" data-aos="fade-down">
                        Welcome to Face Recognition System
                    </h1>
                    <p class="lead text-muted" data-aos="fade-up" data-aos-delay="100">
                        Advanced AI-powered face recognition and monitoring solution
                    </p>
                </div>
            </div>
        </div>
    </div>
    {% endblock %}
</main>



    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- AOS Animation Library -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

    <script>
        // Initialize AOS animations
        AOS.init({
            duration: 800,
            once: true,
            offset: 100
        });

        // Theme Toggle Functionality
        document.addEventListener('DOMContentLoaded', function() {
            const themeToggle = document.getElementById('theme-toggle');
            const themeIcon = document.getElementById('theme-icon');
            const body = document.body;
            
            // Check for saved theme or default to light
            const currentTheme = localStorage.getItem('theme') || 'light';
            
            function setTheme(theme) {
                body.setAttribute('data-bs-theme', theme);
                localStorage.setItem('theme', theme);
                
                if (theme === 'dark') {
                    themeIcon.className = 'bi bi-moon-fill';
                    body.classList.remove('bg-light');
                    body.classList.add('bg-dark');
                } else {
                    themeIcon.className = 'bi bi-sun-fill';
                    body.classList.remove('bg-dark');
                    body.classList.add('bg-light');
                }
            }
            
            // Set initial theme
            setTheme(currentTheme);
            
            // Toggle theme on button click
            themeToggle.addEventListener('click', function() {
                const newTheme = body.getAttribute('data-bs-theme') === 'dark' ? 'light' : 'dark';
                setTheme(newTheme);
            });
        });

        // Toast message handling
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-hide toasts after 5 seconds
            var toasts = document.querySelectorAll('.toast');
            toasts.forEach(function(toast) {
                var bsToast = new bootstrap.Toast(toast);
                if (toast.classList.contains('show')) {
                    setTimeout(function() {
                        bsToast.hide();
                    }, 5000);
                }
            });
        });

        // Dynamic message display function
        function showMessage(message, tags) {
            const container = document.getElementById('toast-container');
            const toastElement = document.createElement('div');
            toastElement.className = 'toast';
            toastElement.setAttribute('role', 'alert');
            toastElement.setAttribute('aria-live', 'assertive');
            toastElement.setAttribute('aria-atomic', 'true');
            
            let iconClass = 'info-circle-fill';
            let tagLabel = 'Info';
            if (tags === 'success') {
                iconClass = 'check-circle-fill';
                tagLabel = 'Success';
            } else if (tags === 'error') {
                iconClass = 'exclamation-triangle-fill';
                tagLabel = 'Error';
            } else if (tags === 'warning') {
                iconClass = 'exclamation-circle-fill';
                tagLabel = 'Warning';
            }
            
            toastElement.innerHTML = `
                <div class="toast-header bg-${tags} text-white border-0">
                    <i class="bi bi-${iconClass} me-2"></i>
                    <strong class="me-auto">${tagLabel}</strong>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body">
                    ${message}
                </div>
            `;
            
            container.appendChild(toastElement);
            const toast = new bootstrap.Toast(toastElement);
            toast.show();
            
            // Auto remove after hiding
            toastElement.addEventListener('hidden.bs.toast', function() {
                container.removeChild(toastElement);
            });
        }

        // Toast Notification System
        function showToast(message, type = 'success', duration = 5000) {
            const toastContainer = document.getElementById('toast-container');
            const toastId = 'toast-' + Date.now();
            
            const toastHTML = `
                <div class="toast align-items-center text-bg-${type} border-0" role="alert" aria-live="assertive" aria-atomic="true" id="${toastId}">
                    <div class="d-flex">
                        <div class="toast-body">
                            <i class="bi bi-${type === 'success' ? 'check-circle-fill' : type === 'danger' ? 'exclamation-triangle-fill' : 'info-circle-fill'} me-2"></i>
                            ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                </div>
            `;
            
            toastContainer.insertAdjacentHTML('beforeend', toastHTML);
            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement, { delay: duration });
            toast.show();
            
            // Remove toast element after it's hidden
            toastElement.addEventListener('hidden.bs.toast', () => {
                toastElement.remove();
            });
        }

        // WebSocket for real-time alerts
        const alertSocket = new WebSocket(
            'ws://' + window.location.host + '/ws/alerts/'
        );

        alertSocket.onmessage = function(e) {
            const data = JSON.parse(e.data);
            if (data.type === 'alert') {
                showToast(data.message, 'warning');
            }
        };

        alertSocket.onclose = function(e) {
            console.error('Alert socket closed unexpectedly');
        };
    </script>

    <!-- Toast Container -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3" id="toast-container" style="z-index: 9999;">
    </div>
</body>
</html>
