import os
import shutil
from django.core.management.base import BaseCommand
from django.conf import settings
from alerts.models import AlertPhoto, Alarm


class Command(BaseCommand):
    help = 'Clean up alert photos from database and filesystem'

    def add_arguments(self, parser):
        parser.add_argument(
            '--confirm',
            action='store_true',
            help='Actually perform the cleanup (without this, it will only show what would be deleted)',
        )
        parser.add_argument(
            '--keep-files',
            action='store_true',
            help='Keep physical files, only clean database records',
        )

    def handle(self, *args, **options):
        confirm = options['confirm']
        keep_files = options['keep_files']
        
        if not confirm:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - Use --confirm to actually delete files')
            )
        
        # Count current records
        alert_photos_count = AlertPhoto.objects.count()
        alarms_with_photos_count = Alarm.objects.filter(alert_photo__isnull=False).count()
        alarms_with_snapshots_count = Alarm.objects.exclude(video_snapshot='').count()
        
        self.stdout.write(f"📊 Current Status:")
        self.stdout.write(f"   - AlertPhoto records: {alert_photos_count}")
        self.stdout.write(f"   - Alarms with alert_photo: {alarms_with_photos_count}")
        self.stdout.write(f"   - Alarms with video_snapshot: {alarms_with_snapshots_count}")
        
        # Alert photos directory
        alert_photos_dir = os.path.join(settings.MEDIA_ROOT, 'alert_photos')
        
        if os.path.exists(alert_photos_dir):
            # Count files in directories
            total_files = 0
            for root, dirs, files in os.walk(alert_photos_dir):
                total_files += len([f for f in files if f.lower().endswith(('.jpg', '.jpeg', '.png'))])
            
            self.stdout.write(f"   - Physical image files: {total_files}")
        else:
            self.stdout.write(f"   - Alert photos directory not found: {alert_photos_dir}")
            total_files = 0
        
        if not confirm:
            self.stdout.write(f"\n🗑️  Would delete:")
            self.stdout.write(f"   - {alert_photos_count} AlertPhoto database records")
            self.stdout.write(f"   - {alarms_with_photos_count} Alarm.alert_photo references (set to NULL)")
            if not keep_files:
                self.stdout.write(f"   - {total_files} physical image files")
            self.stdout.write(f"\n⚠️  Use --confirm to proceed with actual deletion")
            return
        
        # Perform cleanup
        self.stdout.write(f"\n🧹 Starting cleanup...")
        
        try:
            # 1. Clear alert_photo references in Alarms
            if alarms_with_photos_count > 0:
                updated_alarms = Alarm.objects.filter(alert_photo__isnull=False).update(alert_photo=None)
                self.stdout.write(
                    self.style.SUCCESS(f"✅ Cleared alert_photo references in {updated_alarms} Alarm records")
                )
            
            # 2. Delete AlertPhoto records
            if alert_photos_count > 0:
                AlertPhoto.objects.all().delete()
                self.stdout.write(
                    self.style.SUCCESS(f"✅ Deleted {alert_photos_count} AlertPhoto records")
                )
            
            # 3. Delete physical files (if not keeping them)
            if not keep_files and os.path.exists(alert_photos_dir):
                deleted_files = 0
                for root, dirs, files in os.walk(alert_photos_dir):
                    for file in files:
                        if file.lower().endswith(('.jpg', '.jpeg', '.png')):
                            file_path = os.path.join(root, file)
                            try:
                                os.remove(file_path)
                                deleted_files += 1
                            except Exception as e:
                                self.stdout.write(
                                    self.style.ERROR(f"❌ Error deleting {file_path}: {str(e)}")
                                )
                
                # Remove empty directories
                try:
                    for root, dirs, files in os.walk(alert_photos_dir, topdown=False):
                        for dir_name in dirs:
                            dir_path = os.path.join(root, dir_name)
                            try:
                                if not os.listdir(dir_path):  # If directory is empty
                                    os.rmdir(dir_path)
                            except Exception:
                                pass  # Directory not empty or other error
                except Exception:
                    pass
                
                self.stdout.write(
                    self.style.SUCCESS(f"✅ Deleted {deleted_files} physical image files")
                )
            
            # Final status
            remaining_alert_photos = AlertPhoto.objects.count()
            remaining_alarms_with_photos = Alarm.objects.filter(alert_photo__isnull=False).count()
            
            self.stdout.write(f"\n📊 Final Status:")
            self.stdout.write(f"   - AlertPhoto records: {remaining_alert_photos}")
            self.stdout.write(f"   - Alarms with alert_photo: {remaining_alarms_with_photos}")
            
            if remaining_alert_photos == 0 and remaining_alarms_with_photos == 0:
                self.stdout.write(self.style.SUCCESS("🎉 Cleanup completed successfully!"))
            else:
                self.stdout.write(self.style.WARNING("⚠️  Some records may still remain"))
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ Error during cleanup: {str(e)}")
            )
            raise
