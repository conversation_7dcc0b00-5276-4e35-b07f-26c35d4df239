#!/usr/bin/env python3
"""
Integration Example for Unknown Facebank Manager

This script demonstrates how to integrate the UnknownFacebankManager with the 
existing face recognition system to handle unknown persons properly.

Key Integration Points:
1. Initialize unknown manager alongside main recognizer
2. Handle unknown detections by saving to unknown facebank
3. Manage unknown person IDs to prevent rapid incrementation
4. Provide interface for unknown person management

Usage:
    python unknown_integration_example.py

Author: Augment Agent
Created: 2025-09-11
"""

import os
import sys
import django
from pathlib import Path

# Add the demo_server directory to Python path
demo_server_path = Path(__file__).parent
sys.path.insert(0, str(demo_server_path))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'web_server.settings')
django.setup()

from recognition.unknown_facebank_manager import UnknownFacebankManager
from recognition.facenet_recognizer import FaceNetRecognizer
from alerts.models import AlertPhoto, AlertPerson
import numpy as np


class IntegratedRecognitionSystem:
    """
    Example integration of main recognizer with unknown person management.
    
    This class shows how to properly integrate the UnknownFacebankManager
    with the existing FaceNet recognition system.
    """
    
    def __init__(self):
        """Initialize the integrated recognition system."""
        print("🚀 Initializing Integrated Recognition System")
        
        # Initialize main recognizer
        self.main_recognizer = FaceNetRecognizer()
        
        # Initialize unknown person manager with shared model references
        self.unknown_manager = UnknownFacebankManager(
            mtcnn=self.main_recognizer.mtcnn,
            model=self.main_recognizer.model
        )
        
        print("✅ Integrated Recognition System ready")
    
    def recognize_with_unknown_handling(self, image_path, save_unknown=True):
        """
        Perform face recognition with proper unknown person handling.
        
        Args:
            image_path: Path to image for recognition
            save_unknown: Whether to save unknown persons to unknown facebank
            
        Returns:
            dict: Recognition results with unknown person handling
        """
        print(f"🔍 Processing image: {image_path}")
        
        try:
            # Perform main recognition
            from PIL import Image
            image = Image.open(image_path)
            bboxes, names, scores = self.main_recognizer.recognize(image)
            
            results = {
                'bboxes': bboxes,
                'names': names,
                'scores': scores,
                'unknown_persons_added': [],
                'unknown_persons_existing': []
            }
            
            # Handle unknown persons
            for i, name in enumerate(names):
                if name == "Unknown":
                    if save_unknown:
                        # Check if this unknown person should be saved
                        unknown_id = self._handle_unknown_person(image_path, bboxes[i])
                        if unknown_id:
                            if self.unknown_manager.unknown_person_exists(unknown_id):
                                results['unknown_persons_existing'].append(unknown_id)
                            else:
                                results['unknown_persons_added'].append(unknown_id)
            
            return results
            
        except Exception as e:
            print(f"❌ Error in recognition: {str(e)}")
            return None
    
    def _handle_unknown_person(self, image_path, bbox):
        """
        Handle detection of an unknown person.
        
        Args:
            image_path: Path to the image
            bbox: Bounding box of the detected face
            
        Returns:
            str: Unknown person ID if handled successfully, None otherwise
        """
        try:
            # In a real implementation, you might:
            # 1. Extract the face region using the bbox
            # 2. Save the face image to a temporary location
            # 3. Add it to the unknown facebank
            # 4. Optionally save to database
            
            # For this example, we'll use the full image
            # In practice, you'd crop to the face region
            
            # Generate next unknown ID
            unknown_id = self.unknown_manager.get_next_unknown_id()
            
            # Add to unknown facebank
            success = self.unknown_manager.add_unknown_person(unknown_id, image_path)
            
            if success:
                print(f"✅ Added unknown person: {unknown_id}")
                return unknown_id
            else:
                print(f"❌ Failed to add unknown person")
                return None
                
        except Exception as e:
            print(f"❌ Error handling unknown person: {str(e)}")
            return None
    
    def get_unknown_persons_summary(self):
        """Get summary of unknown persons in the system."""
        info = self.unknown_manager.get_facebank_info()
        
        summary = {
            'total_unknown_persons': info['total_persons'],
            'unknown_person_names': info['person_names'],
            'total_embeddings': info['total_embeddings'],
            'next_available_id': self.unknown_manager.get_next_unknown_id()
        }
        
        return summary
    
    def cleanup_unknown_persons(self, keep_recent=10):
        """
        Clean up old unknown persons, keeping only recent ones.
        
        Args:
            keep_recent: Number of recent unknown persons to keep
        """
        try:
            unknown_names = self.unknown_manager.get_unknown_person_names()
            
            if len(unknown_names) <= keep_recent:
                print(f"📊 Only {len(unknown_names)} unknown persons, no cleanup needed")
                return
            
            # Sort by ID number and remove oldest
            def get_id_number(name):
                try:
                    return int(name.split('_')[1])
                except:
                    return 0
            
            sorted_names = sorted(unknown_names, key=get_id_number)
            to_remove = sorted_names[:-keep_recent]
            
            print(f"🧹 Cleaning up {len(to_remove)} old unknown persons")
            
            for name in to_remove:
                success = self.unknown_manager.remove_unknown_person(name)
                if success:
                    print(f"   ✅ Removed {name}")
                else:
                    print(f"   ❌ Failed to remove {name}")
            
            print(f"✅ Cleanup complete, kept {keep_recent} recent unknown persons")
            
        except Exception as e:
            print(f"❌ Error during cleanup: {str(e)}")


def demo_integration():
    """Demonstrate the integrated recognition system."""
    
    print("🎯 Unknown Person Integration Demo")
    print("=" * 50)
    
    try:
        # Initialize integrated system
        system = IntegratedRecognitionSystem()
        
        # Show initial state
        print("\n📊 Initial Unknown Persons Summary:")
        summary = system.get_unknown_persons_summary()
        for key, value in summary.items():
            print(f"   {key}: {value}")
        
        # Look for test images
        import glob
        test_images = []
        
        # Look in unknowns directory
        unknowns_dir = "./media/alert_photos/unknowns"
        if os.path.exists(unknowns_dir):
            for ext in ['*.jpg', '*.jpeg', '*.png']:
                test_images.extend(glob.glob(os.path.join(unknowns_dir, "**", ext), recursive=True))
        
        if test_images:
            print(f"\n🖼️ Found {len(test_images)} test images")
            
            # Test recognition with unknown handling
            test_image = test_images[0]
            print(f"\n🔍 Testing recognition with: {test_image}")
            
            results = system.recognize_with_unknown_handling(test_image, save_unknown=True)
            
            if results:
                print("📋 Recognition Results:")
                print(f"   Detected faces: {len(results['names'])}")
                print(f"   Names: {results['names']}")
                print(f"   New unknown persons: {results['unknown_persons_added']}")
                print(f"   Existing unknown persons: {results['unknown_persons_existing']}")
        
        # Show final state
        print("\n📊 Final Unknown Persons Summary:")
        final_summary = system.get_unknown_persons_summary()
        for key, value in final_summary.items():
            print(f"   {key}: {value}")
        
        # Demonstrate cleanup (optional)
        if final_summary['total_unknown_persons'] > 5:
            print("\n🧹 Demonstrating cleanup (keeping 3 recent):")
            system.cleanup_unknown_persons(keep_recent=3)
            
            cleanup_summary = system.get_unknown_persons_summary()
            print(f"   After cleanup: {cleanup_summary['total_unknown_persons']} unknown persons")
        
        print("\n✅ Integration demo completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ Demo failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = demo_integration()
    if success:
        print("\n🎉 Integration working correctly!")
    else:
        print("\n💥 Integration demo failed!")
        sys.exit(1)
