"""
Test script to validate the critical fixes for face recognition system
"""
import os
import sys
import django
import numpy as np
from unittest.mock import Mock

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'web_server.settings')
django.setup()

from videostream.services.person_service import PersonService
from videostream.services.face_processing_service import FaceProcessingService
from alerts.models import AlertPerson
from django.core.cache import cache


def test_unknown_person_numbering():
    """Test unknown person numbering system"""
    print("🧪 Testing Unknown Person Numbering System...")
    
    try:
        # Clear any existing cache
        cache.clear()
        
        # Create PersonService instance
        person_service = PersonService()
        
        # Check initial counter
        print(f"   Initial unknown_count: {person_service.unknown_count}")
        
        # Check database state
        existing_unknowns = AlertPerson.objects.filter(
            name__startswith="Unknown_",
            is_unknown=True
        ).count()
        print(f"   Existing unknown persons in DB: {existing_unknowns}")
        
        # Test counter initialization logic
        person_service._initialize_unknown_count()
        print(f"   After re-initialization: {person_service.unknown_count}")
        
        print("   ✅ Unknown person numbering system initialized correctly")
        
    except Exception as e:
        print(f"   ❌ Unknown person numbering test failed: {str(e)}")


def test_cache_cooldown_system():
    """Test alarm cooldown cache system"""
    print("🧪 Testing Alarm Cooldown Cache System...")
    
    try:
        # Clear cache
        cache.clear()
        
        # Create mock objects
        mock_person = Mock()
        mock_person.id = 1
        mock_person.name = "TestPerson"
        mock_person.user.id = 1
        
        mock_camera = Mock()
        mock_camera.id = 1
        mock_camera.name = "TestCamera"
        
        # Create service
        service = FaceProcessingService(camera_id=1)
        
        # Test cache key generation
        cache_key = f"last_alert_{mock_person.id}_{mock_camera.id}"
        print(f"   Cache key format: {cache_key}")
        
        # Test cache operations
        from django.utils import timezone
        current_time = timezone.now()
        timestamp = current_time.timestamp()
        
        # Set cache with timeout
        cache.set(cache_key, timestamp, timeout=120)
        
        # Retrieve and verify
        cached_value = cache.get(cache_key)
        if cached_value is not None:
            print(f"   ✅ Cache set/get working: {cached_value}")
            
            # Test time difference calculation
            time_diff = timestamp - cached_value
            print(f"   Time difference calculation: {time_diff}")
        else:
            print("   ❌ Cache set/get failed")
        
        print("   ✅ Cache cooldown system working correctly")
        
    except Exception as e:
        print(f"   ❌ Cache cooldown test failed: {str(e)}")


def test_service_imports():
    """Test that all services can be imported correctly"""
    print("🧪 Testing Service Imports...")
    
    try:
        from videostream.services.face_processing_service import FaceProcessingService
        from videostream.services.person_service import PersonService
        from videostream.services.alert_service import AlertService
        from videostream.services.recognition_service import RecognitionService
        from videostream.services.zone_service import ZoneService
        from videostream.services.drawing_service import DrawingService
        
        print("   ✅ All services imported successfully")
        
        # Test service instantiation
        person_service = PersonService()
        face_service = FaceProcessingService(camera_id=1)
        
        print("   ✅ Services can be instantiated")
        
    except Exception as e:
        print(f"   ❌ Service import test failed: {str(e)}")


def test_directory_creation():
    """Test unknown person directory creation"""
    print("🧪 Testing Directory Creation for Unknown Persons...")

    try:
        import os
        from django.conf import settings

        # Test directory structure
        base_dir = settings.MEDIA_ROOT
        unknown_dir = os.path.join(base_dir, 'alert_photos', 'unknowns')

        print(f"   Base directory: {base_dir}")
        print(f"   Unknown directory: {unknown_dir}")

        # Test person directory creation
        test_person_name = "Unknown_999"
        person_dir = os.path.join(unknown_dir, test_person_name)

        # Create test directory
        os.makedirs(person_dir, exist_ok=True)

        if os.path.exists(person_dir):
            print(f"   ✅ Directory creation working: {person_dir}")

            # Clean up test directory
            try:
                os.rmdir(person_dir)
                print(f"   ✅ Directory cleanup successful")
            except:
                print(f"   ⚠️ Directory cleanup failed (may contain files)")
        else:
            print(f"   ❌ Directory creation failed")

    except Exception as e:
        print(f"   ❌ Directory creation test failed: {str(e)}")


def main():
    """Run all tests"""
    print("🚀 Running Face Recognition System Fix Validation Tests\n")

    test_service_imports()
    print()

    test_unknown_person_numbering()
    print()

    test_cache_cooldown_system()
    print()

    test_directory_creation()
    print()

    print("🎯 Test validation complete!")


if __name__ == "__main__":
    main()
