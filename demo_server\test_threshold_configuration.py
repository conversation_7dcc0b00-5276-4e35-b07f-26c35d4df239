#!/usr/bin/env python3
"""
Test script to verify threshold values are properly read from settings

This script verifies that all threshold values are centrally managed
and read from the RECOGNITION_CONFIG in settings.py.

Usage:
    python test_threshold_configuration.py

Author: Augment Agent
Created: 2025-09-11
"""

import os
import sys
import django
import logging
from pathlib import Path

# Add the demo_server directory to Python path
demo_server_path = Path(__file__).parent
sys.path.insert(0, str(demo_server_path))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'web_server.settings')
django.setup()

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

from recognition.facenet_recognizer import FaceNetRecognizer
from django.conf import settings


def test_threshold_configuration():
    """Test that threshold values are properly read from settings."""
    
    print("🧪 Testing Threshold Configuration")
    print("=" * 50)
    
    try:
        # Check settings configuration
        print("📋 Checking RECOGNITION_CONFIG in settings...")
        config = settings.RECOGNITION_CONFIG
        
        print(f"   Known threshold: {config.get('known_threshold', 'NOT SET')}")
        print(f"   Unknown threshold: {config.get('unknown_threshold', 'NOT SET')}")
        print(f"   Enable unknown facebank: {config.get('enable_unknown_facebank', 'NOT SET')}")
        
        # Verify required keys exist
        required_keys = ['known_threshold', 'unknown_threshold', 'enable_unknown_facebank']
        missing_keys = []
        
        for key in required_keys:
            if key not in config:
                missing_keys.append(key)
        
        if missing_keys:
            print(f"❌ Missing required configuration keys: {missing_keys}")
            return False
        
        print("✅ All required configuration keys present")
        
        # Initialize recognizer and check if it uses the same values
        print("\n📋 Initializing FaceNet recognizer...")
        recognizer = FaceNetRecognizer()
        
        print(f"   Recognizer config - Known threshold: {recognizer.conf['known_threshold']}")
        print(f"   Recognizer config - Unknown threshold: {recognizer.conf['unknown_threshold']}")
        print(f"   Recognizer config - Enable unknown facebank: {recognizer.conf['enable_unknown_facebank']}")
        
        # Verify values match
        if (recognizer.conf['known_threshold'] == config['known_threshold'] and
            recognizer.conf['unknown_threshold'] == config['unknown_threshold'] and
            recognizer.conf['enable_unknown_facebank'] == config['enable_unknown_facebank']):
            print("✅ Recognizer is using the same configuration values")
        else:
            print("❌ Configuration mismatch between settings and recognizer")
            return False
        
        # Test threshold value types
        print("\n📋 Checking threshold value types...")
        
        known_threshold = config['known_threshold']
        unknown_threshold = config['unknown_threshold']
        
        if not isinstance(known_threshold, (int, float)):
            print(f"❌ Known threshold should be numeric, got {type(known_threshold)}")
            return False
        
        if not isinstance(unknown_threshold, (int, float)):
            print(f"❌ Unknown threshold should be numeric, got {type(unknown_threshold)}")
            return False
        
        print(f"✅ Known threshold type: {type(known_threshold).__name__} = {known_threshold}")
        print(f"✅ Unknown threshold type: {type(unknown_threshold).__name__} = {unknown_threshold}")
        
        # Test threshold value ranges
        print("\n📋 Checking threshold value ranges...")
        
        if known_threshold <= 0:
            print(f"❌ Known threshold should be positive, got {known_threshold}")
            return False
        
        if unknown_threshold <= 0:
            print(f"❌ Unknown threshold should be positive, got {unknown_threshold}")
            return False
        
        if unknown_threshold <= known_threshold:
            print(f"⚠️ Warning: Unknown threshold ({unknown_threshold}) should typically be higher than known threshold ({known_threshold})")
        
        print(f"✅ Threshold values are in valid ranges")
        
        # Test configuration modification simulation
        print("\n📋 Testing configuration access patterns...")
        
        # Simulate how the code accesses configuration
        try:
            test_known = recognizer.conf['known_threshold']
            test_unknown = recognizer.conf['unknown_threshold']
            test_enable = recognizer.conf['enable_unknown_facebank']
            
            print(f"✅ Direct access works: known={test_known}, unknown={test_unknown}, enable={test_enable}")
        except KeyError as e:
            print(f"❌ Direct access failed: {e}")
            return False
        
        print("\n🎉 All threshold configuration tests passed!")
        print("\n📝 Summary:")
        print(f"   • Known persons threshold: {known_threshold}")
        print(f"   • Unknown persons threshold: {unknown_threshold}")
        print(f"   • Unknown facebank enabled: {config['enable_unknown_facebank']}")
        print(f"   • Configuration source: settings.RECOGNITION_CONFIG")
        print(f"   • All values centrally managed: ✅")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def show_configuration_guide():
    """Show how to modify threshold configuration."""
    
    print("\n📖 Configuration Guide")
    print("=" * 30)
    print("To modify threshold values:")
    print("1. Edit demo_server/web_server/settings.py")
    print("2. Find RECOGNITION_CONFIG dictionary")
    print("3. Modify threshold values:")
    print("   - 'known_threshold': for known persons (current: 1.35)")
    print("   - 'unknown_threshold': for unknown persons (current: 1.55)")
    print("4. Restart the application")
    print("\nExample:")
    print("RECOGNITION_CONFIG = {")
    print("    # ... other settings ...")
    print("    'known_threshold': 1.35,    # Lower = more strict")
    print("    'unknown_threshold': 1.55,  # Higher = groups more faces")
    print("}")


if __name__ == "__main__":
    print("🚀 Starting Threshold Configuration Test")
    print("=" * 50)
    
    success = test_threshold_configuration()
    
    if success:
        show_configuration_guide()
        print("\n✅ Threshold configuration is properly centralized!")
    else:
        print("\n💥 Threshold configuration test failed!")
        sys.exit(1)
