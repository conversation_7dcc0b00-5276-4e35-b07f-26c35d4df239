import logging
import uuid
from django.core.cache import cache
from django.core.files.base import ContentFile
from django.utils import timezone
from asgiref.sync import sync_to_async
from channels.layers import get_channel_layer
import cv2

# Import our services
from .recognition_service import RecognitionService
from .zone_service import ZoneService
from .alert_service import AlertService
from .person_service import PersonService
from .drawing_service import DrawingService

# Import models
from alerts.models import Alarm, AlertPerson

logger = logging.getLogger(__name__)


class FaceProcessingService:
    """Main orchestrator service for face processing pipeline"""
    
    def __init__(self, camera_id=None):
        self.camera_id = camera_id
        self.person_service = PersonService()
        self.alert_cooldown = 60  # seconds
    
    async def process_frame(self, frame, camera, filter_by_zone=False, zones=None):
        """
        Main processing pipeline for face recognition
        
        Args:
            frame: Input video frame
            camera: Camera object
            filter_by_zone: Whether to apply zone filtering
            zones: List of zone definitions
            
        Returns:
            Processed frame with annotations
        """
        try:
            # Step 1: Face Recognition
            bboxes, names, confidences = await RecognitionService.detect_faces(frame, camera.id)
            
            if bboxes is None or len(bboxes) == 0:
                return frame
            
            # Step 2: Zone Filtering (if enabled)
            if filter_by_zone and zones:
                bboxes, names, confidences = ZoneService.filter_detections_by_zones(
                    bboxes, names, confidences, zones, frame.shape
                )
                
                if len(bboxes) == 0:
                    return frame
            
            # Step 3: Handle Multiple Faces (only for known persons)
            alert_photo = await AlertService.handle_multiple_faces(frame, camera, names, confidences)
            
            # Step 4: Process Individual Detections
            processed_names = await self._process_individual_detections(
                bboxes, names, confidences, frame, camera, alert_photo
            )
            
            # Step 5: Draw Annotations
            annotated_frame = DrawingService.annotate_frame(frame, bboxes, processed_names, confidences)
            
            return annotated_frame
            
        except Exception as e:
            logger.error(f"Error in face processing pipeline: {str(e)}")
            return frame
    
    async def _process_individual_detections(self, bboxes, names, confidences, frame, camera, alert_photo):
        """
        Process each detected face individually
        
        Args:
            bboxes: Face bounding boxes
            names: Detected names
            confidences: Confidence scores
            frame: Video frame
            camera: Camera object
            alert_photo: Multiple face photo (if any)
            
        Returns:
            List of processed names (may include newly created unknown persons)
        """
        processed_names = []
        
        for bbox, name, confidence in zip(bboxes, names, confidences):
            try:
                display_name = name
                
                # Handle unknown persons
                if name == "Unknown":
                    alert_person, new_name = await self.person_service.process_unknown_person(frame, camera)
                    if alert_person and new_name:
                        display_name = new_name  # This will be "Unknown_X"
                        logger.info(f"✅ New unknown person created: {new_name}")
                        logger.debug(f"   - Database ID: {alert_person.id}")
                        logger.debug(f"   - Display name: {display_name}")
                    else:
                        # Fallback to generic "Unknown" if creation failed
                        display_name = "Unknown"
                        logger.warning("❌ Failed to save new unknown person - using generic 'Unknown'")
                
                # Handle known persons
                elif not display_name.startswith("Unknown_"):
                    # Update last seen date
                    person = await PersonService.update_person_last_seen(display_name)
                    
                    if person:
                        # Create alert for known person
                        alert_result = await self._create_person_alert(
                            camera, display_name, confidence, frame, alert_photo, person
                        )
                        
                        # Send WebSocket notification
                        if alert_result:
                            await self._send_websocket_notification(alert_result)
                
                processed_names.append(display_name)
                
            except Exception as e:
                logger.error(f"Error processing individual detection: {str(e)}")
                processed_names.append(name)  # Fallback to original name
        
        return processed_names

    async def _create_person_alert(self, camera, name, confidence, frame, master_photo, person):
        """
        Create alert for known person

        Args:
            camera: Camera object
            name: Person name
            confidence: Recognition confidence
            frame: Video frame
            master_photo: Multiple face photo (if any)
            person: AlertPerson instance

        Returns:
            Alert result dict or None
        """
        try:
            if "Unknown" in name:
                return None

            # Check cooldown with proper cache handling
            cache_key = f"last_alert_{person.id}_{camera.id}"
            last_alert_time = cache.get(cache_key)
            current_time = timezone.now()

            # Convert to timestamp for reliable comparison
            current_timestamp = current_time.timestamp()

            # Check if cooldown period has passed
            cooldown_expired = True
            if last_alert_time is not None:
                try:
                    # Handle both timestamp and datetime objects
                    if isinstance(last_alert_time, (int, float)):
                        last_timestamp = last_alert_time
                    else:
                        last_timestamp = last_alert_time.timestamp()

                    time_diff = current_timestamp - last_timestamp
                    cooldown_expired = time_diff >= self.alert_cooldown

                    if not cooldown_expired:
                        logger.debug(f"Alert cooldown active for {person.name} on camera {camera.id}. "
                                   f"Time remaining: {self.alert_cooldown - time_diff:.1f}s")
                        return None

                except Exception as e:
                    logger.warning(f"Error processing cooldown cache for {person.name}: {str(e)}")
                    # If cache is corrupted, allow the alert
                    cooldown_expired = True

            if cooldown_expired:
                # Create alarm using sync method for better reliability
                alarm_result = await sync_to_async(self._create_alarm_sync)(
                    person, camera, confidence, frame, master_photo
                )

                if alarm_result:
                    # Update cache with proper timeout (set to 2x cooldown for safety)
                    cache.set(cache_key, current_timestamp, timeout=self.alert_cooldown * 2)

                    logger.info(f"✅ Created alarm for {person.name} on camera {camera.name} "
                              f"(confidence: {confidence:.2f})")
                else:
                    logger.error(f"❌ Failed to create alarm for {person.name}")
                    return None

                # Return alert result for WebSocket notification
                return {
                    "user_id": person.user.id,
                    "name": person.name,
                    "camera_name": camera.name,
                    "confidence": confidence
                }

            return None

        except Exception as e:
            logger.error(f"Error creating person alert: {str(e)}")
            return None

    def _create_alarm_sync(self, person, camera, confidence, frame, master_photo):
        """
        Synchronous method to create alarm in database
        Following original working logic for reliability

        Args:
            person: AlertPerson instance
            camera: Camera object
            confidence: Recognition confidence
            frame: Video frame
            master_photo: Multiple face photo (if any)

        Returns:
            bool: True if alarm created successfully
        """
        try:
            from alerts.models import Alarm
            import uuid
            import cv2
            from django.core.files.base import ContentFile

            # Create alarm
            alarm = Alarm(
                person=person,
                camera=camera,
                confidence=confidence,
                alert_photo=master_photo  # Use shared photo for multiple faces
            )

            # Save individual snapshot if no master photo (original logic)
            if not master_photo:
                _, buffer = cv2.imencode('.jpg', frame)
                content = ContentFile(buffer.tobytes())
                alarm.video_snapshot.save(
                    f'{person.name}_snapshot_{uuid.uuid4()}.jpg',
                    content
                )

            # Save alarm to database
            alarm.save()

            logger.debug(f"Alarm saved to database: Alarm.id={alarm.id}")
            return True

        except Exception as e:
            logger.error(f"Failed to create alarm in database: {str(e)}")
            return False

    async def _send_websocket_notification(self, alert_result):
        """
        Send WebSocket notification for alert

        Args:
            alert_result: Alert result dictionary
        """
        try:
            channel_layer = get_channel_layer()
            await channel_layer.group_send(
                f"user_{alert_result['user_id']}",
                {
                    "type": "send_alert",
                    "message": f"Alert: {alert_result['name']} detected on camera {alert_result['camera_name']} with confidence {alert_result['confidence']:.2f}"
                }
            )
            logger.debug(f"WebSocket notification sent for {alert_result['name']}")

        except Exception as e:
            logger.error(f"Error sending WebSocket notification: {str(e)}")

    @classmethod
    def cleanup(cls):
        """Cleanup service resources"""
        try:
            RecognitionService.cleanup()
            logger.info("Face processing service cleaned up")
        except Exception as e:
            logger.error(f"Error during cleanup: {str(e)}")
