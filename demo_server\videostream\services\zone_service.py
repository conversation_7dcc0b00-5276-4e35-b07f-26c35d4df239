import logging
import numpy as np
from videostream.zone_utils import scale_zone_points, is_point_in_polygon

logger = logging.getLogger(__name__)


class ZoneService:
    """Service for handling zone-based filtering operations"""
    
    @staticmethod
    def is_face_in_zone(face_bbox, zone_points, video_width, video_height):
        """
        Check if a face is inside a polygon zone.
        
        Args:
            face_bbox: Face bounding box [x1, y1, x2, y2]
            zone_points: List of points defining the polygon zone [[x1,y1], [x2,y2], ...]
            video_width: Width of the video frame
            video_height: Height of the video frame
            
        Returns:
            bool: True if the face is inside the zone, False otherwise
        """
        try:
            # Calculate the bottom center point of the face bounding box
            x1, y1, x2, y2 = map(int, face_bbox[:4])
            bottom_center_x = (x1 + x2) // 2
            bottom_center_y = y2
            point = (bottom_center_x, bottom_center_y)
            
            # Scale zone points to match video dimensions
            scaled_polygon = scale_zone_points(zone_points, video_width, video_height)
            
            return is_point_in_polygon(point, scaled_polygon)
        except Exception as e:
            logger.error(f"Error checking if face is in zone: {str(e)}")
            return False
    
    @staticmethod
    def filter_detections_by_zones(bboxes, names, confidences, zones, frame_shape):
        """
        Filter face detections based on zone constraints
        
        Args:
            bboxes: Array of face bounding boxes
            names: List of recognized names
            confidences: List of confidence scores
            zones: List of zone definitions
            frame_shape: Shape of the video frame (height, width, channels)
            
        Returns:
            tuple: Filtered (bboxes, names, confidences)
        """
        try:
            if not zones or len(zones) == 0:
                return bboxes, names, confidences
            
            video_height, video_width = frame_shape[:2]
            
            # Create filtered lists for faces within zones
            filtered_bboxes = []
            filtered_names = []
            filtered_confidences = []
            
            for bbox, name, confidence in zip(bboxes, names, confidences):
                face_in_any_zone = False
                
                # Check if face is in any of the defined zones
                for zone in zones:
                    if ZoneService.is_face_in_zone(bbox, zone['points'], video_width, video_height):
                        face_in_any_zone = True
                        break
                
                if face_in_any_zone:
                    filtered_bboxes.append(bbox)
                    filtered_names.append(name)
                    filtered_confidences.append(confidence)
            
            # Convert back to numpy array if we have results
            if len(filtered_bboxes) > 0:
                filtered_bboxes = np.array(filtered_bboxes)
                logger.debug(f"Zone filtering: {len(bboxes)} -> {len(filtered_bboxes)} faces")
                return filtered_bboxes, filtered_names, filtered_confidences
            else:
                logger.debug("Zone filtering: No faces in zones")
                return np.array([]), [], []
                
        except Exception as e:
            logger.error(f"Error in zone filtering: {str(e)}")
            # Return original data on error
            return bboxes, names, confidences
