"""
Unknown Person Face Bank Manager

This module provides functionality to manage a separate face bank system specifically 
for unknown persons detected by the face recognition system. It maintains separate
unknown_facebank.pth and unknown_names.npy files to track unknown individuals
without interfering with the main face recognition system.

Key Features:
- Separate face bank for unknown persons
- Incremental naming (Unknown_1, Unknown_2, etc.)
- Face embedding storage and management
- Integration with existing face recognition patterns
- Proper error handling and logging

"""

import torch
import numpy as np
import os
import io
import logging
from PIL import Image
from django.conf import settings
from typing import Optional, Tuple, List, Dict, Any
import shutil
from datetime import datetime

logger = logging.getLogger(__name__)


class UnknownFacebankManager:
    """
    Manages a separate face bank system for unknown persons.
    
    This class handles the creation, loading, saving, and management of
    unknown person face embeddings and their associated names/IDs.
    """
    
    def __init__(self, mtcnn=None, model=None):
        """
        Initialize the Unknown Facebank Manager.
        
        Args:
            mtcnn: MTCNN face detector instance (optional)
            model: FaceNet model instance (optional)
        """
        logger.info("Initializing UnknownFacebankManager")
        
        # Get configuration from Django settings
        self.conf = settings.RECOGNITION_CONFIG
        
        # Set up paths for unknown person face bank
        self.unknown_facebank_dir = os.path.join(self.conf['facebank_path'], 'unknowns')
        self.unknown_facebank_path = os.path.join(self.unknown_facebank_dir, 'unknown_facebank.pth')
        self.unknown_names_path = os.path.join(self.unknown_facebank_dir, 'unknown_names.npy')
        
        # Ensure the unknowns directory exists
        os.makedirs(self.unknown_facebank_dir, exist_ok=True)
        
        # Store model references (will be injected from main recognizer)
        self.mtcnn = mtcnn
        self.model = model
        
        # Initialize face bank data
        self.embeddings = None
        self.names = None
        
        # Load existing unknown face bank
        self._load_unknown_facebank()
        
        logger.info("UnknownFacebankManager initialization complete")
    
    def _load_unknown_facebank(self):
        """
        Load existing unknown face bank data from files.
        
        If files don't exist, initialize empty face bank.
        """
        try:
            if (os.path.exists(self.unknown_facebank_path) and 
                os.path.exists(self.unknown_names_path)):
                
                logger.info("Loading existing unknown face bank")
                
                # Load embeddings
                self.embeddings = torch.load(
                    self.unknown_facebank_path, 
                    map_location=self.conf['device'], 
                    weights_only=True
                )
                
                # Load names
                self.names = np.load(self.unknown_names_path, allow_pickle=True)
                
                logger.info(f"Loaded unknown face bank with {len(self.names)} persons")
                
            else:
                logger.info("No existing unknown face bank found, initializing empty")
                self._initialize_empty_facebank()
                
        except Exception as e:
            logger.error(f"Error loading unknown face bank: {str(e)}")
            logger.info("Initializing empty face bank due to loading error")
            self._initialize_empty_facebank()
    
    def _initialize_empty_facebank(self):
        """Initialize empty face bank data structures."""
        self.embeddings = None
        self.names = np.array([])
        self._save_unknown_facebank()
        logger.info("Initialized empty unknown face bank")
    
    def _save_unknown_facebank(self):
        """
        Save current face bank data to files.
        
        Returns:
            bool: True if save was successful, False otherwise
        """
        try:
            # Create backup of existing files if they exist
            self._create_backup()
            
            # Save embeddings
            torch.save(self.embeddings, self.unknown_facebank_path)
            
            # Save names
            np.save(self.unknown_names_path, self.names)
            
            logger.info("Successfully saved unknown face bank")
            return True
            
        except Exception as e:
            logger.error(f"Error saving unknown face bank: {str(e)}")
            return False
    
    def _create_backup(self):
        """Create backup of existing face bank files."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        if os.path.exists(self.unknown_facebank_path):
            backup_path = f"{self.unknown_facebank_path}.backup_{timestamp}"
            shutil.copy2(self.unknown_facebank_path, backup_path)

        if os.path.exists(self.unknown_names_path):
            backup_path = f"{self.unknown_names_path}.backup_{timestamp}"
            shutil.copy2(self.unknown_names_path, backup_path)

    def _generate_next_unknown_id(self) -> str:
        """
        Generate the next available unknown person ID.

        Returns:
            str: Next available ID in format "Unknown_X"
        """
        if self.names is None or len(self.names) == 0:
            return "Unknown_1"

        # Find the highest existing number
        max_num = 0
        for name in self.names:
            if name.startswith("Unknown_"):
                try:
                    num = int(name.split("_")[1])
                    max_num = max(max_num, num)
                except (IndexError, ValueError):
                    continue

        return f"Unknown_{max_num + 1}"

    def _process_face_image(self, image_path: str) -> Optional[torch.Tensor]:
        """
        Process a face image and generate its embedding.

        Args:
            image_path: Path to the face image

        Returns:
            torch.Tensor: Face embedding or None if processing failed
        """
        if self.model is None or self.mtcnn is None:
            logger.error("Model or MTCNN not available for face processing")
            return None

        try:
            # Load and process image
            img = Image.open(image_path)

            # Align face if needed
            if img.size != (112, 112):
                try:
                    img = self.mtcnn.align(img, self.conf['min_face_size'])
                except Exception as e:
                    logger.error(f"Error aligning face image {image_path}: {str(e)}")
                    return None

            if img is None:
                logger.error(f"Face alignment failed for {image_path}")
                return None

            # Generate embedding
            with torch.no_grad():
                embedding = self.model(
                    self.conf['test_transform'](img).to(self.conf['device']).unsqueeze(0)
                )

            logger.info(f"Successfully generated embedding for {image_path}")
            return embedding

        except Exception as e:
            logger.error(f"Error processing face image {image_path}: {str(e)}")
            return None

    def add_unknown_person(self, unknown_id: str, image_path: str) -> bool:
        """
        Add a new unknown person to the face bank.

        Args:
            unknown_id: ID for the unknown person (e.g., "Unknown_1")
            image_path: Path to the face image

        Returns:
            bool: True if addition was successful, False otherwise
        """
        try:
            logger.info(f"Adding unknown person {unknown_id} from {image_path}")

            # Check if ID already exists
            if self.names is not None and unknown_id in self.names:
                logger.warning(f"Unknown person {unknown_id} already exists in face bank")
                return False

            # Process the face image
            embedding = self._process_face_image(image_path)
            if embedding is None:
                logger.error(f"Failed to process face image for {unknown_id}")
                return False

            # Add to face bank
            if self.embeddings is None:
                self.embeddings = embedding
                self.names = np.array([unknown_id])
            else:
                self.embeddings = torch.cat([self.embeddings, embedding])
                self.names = np.append(self.names, unknown_id)

            # Save updated face bank
            if self._save_unknown_facebank():
                logger.info(f"Successfully added unknown person {unknown_id}")
                return True
            else:
                logger.error(f"Failed to save face bank after adding {unknown_id}")
                return False

        except Exception as e:
            logger.error(f"Error adding unknown person {unknown_id}: {str(e)}")
            return False

    def remove_unknown_person(self, unknown_id: str) -> bool:
        """
        Remove an unknown person from the face bank.

        Args:
            unknown_id: ID of the unknown person to remove

        Returns:
            bool: True if removal was successful, False otherwise
        """
        try:
            logger.info(f"Removing unknown person {unknown_id}")

            if self.names is None or len(self.names) == 0:
                logger.warning("No unknown persons in face bank to remove")
                return False

            # Find indices of the person to remove
            person_indices = [i for i, name in enumerate(self.names) if name == unknown_id]

            if not person_indices:
                logger.warning(f"Unknown person {unknown_id} not found in face bank")
                return False

            # Remove all instances (reverse order to maintain indices)
            for idx in reversed(person_indices):
                if idx == 0:
                    if len(self.names) > 1:
                        self.embeddings = self.embeddings[1:]
                        self.names = self.names[1:]
                    else:
                        # Last person, reset to empty
                        self.embeddings = None
                        self.names = np.array([])
                        break
                else:
                    self.embeddings = torch.cat([self.embeddings[:idx], self.embeddings[idx+1:]])
                    self.names = np.concatenate([self.names[:idx], self.names[idx+1:]])

            # Save updated face bank
            if self._save_unknown_facebank():
                logger.info(f"Successfully removed {len(person_indices)} instances of {unknown_id}")
                return True
            else:
                logger.error(f"Failed to save face bank after removing {unknown_id}")
                return False

        except Exception as e:
            logger.error(f"Error removing unknown person {unknown_id}: {str(e)}")
            return False

    def get_unknown_person_count(self) -> int:
        """
        Get the total number of unknown persons in the face bank.

        Returns:
            int: Number of unique unknown persons
        """
        if self.names is None or len(self.names) == 0:
            return 0
        return len(set(self.names))

    def get_unknown_person_names(self) -> List[str]:
        """
        Get list of all unknown person names/IDs.

        Returns:
            List[str]: List of unique unknown person IDs
        """
        if self.names is None or len(self.names) == 0:
            return []
        return list(set(self.names))

    def get_facebank_info(self) -> Dict[str, Any]:
        """
        Get comprehensive information about the unknown face bank.

        Returns:
            Dict: Information about the face bank including counts, names, etc.
        """
        info = {
            'total_embeddings': 0,
            'total_persons': 0,
            'person_names': [],
            'facebank_path': self.unknown_facebank_path,
            'names_path': self.unknown_names_path,
            'facebank_exists': os.path.exists(self.unknown_facebank_path),
            'names_exists': os.path.exists(self.unknown_names_path)
        }

        if self.embeddings is not None:
            info['total_embeddings'] = self.embeddings.shape[0]
            info['embedding_dimension'] = self.embeddings.shape[1]

        if self.names is not None and len(self.names) > 0:
            unique_names = list(set(self.names))
            info['total_persons'] = len(unique_names)
            info['person_names'] = sorted(unique_names)

            # Add per-person embedding counts
            info['person_embedding_counts'] = {}
            for name in unique_names:
                count = list(self.names).count(name)
                info['person_embedding_counts'][name] = count

        return info

    def add_unknown_person_auto_id(self, image_path: str) -> Optional[str]:
        """
        Add an unknown person with automatically generated ID.

        Args:
            image_path: Path to the face image

        Returns:
            str: Generated unknown ID if successful, None otherwise
        """
        try:
            unknown_id = self._generate_next_unknown_id()

            if self.add_unknown_person(unknown_id, image_path):
                logger.info(f"Successfully added unknown person with auto-generated ID: {unknown_id}")
                return unknown_id
            else:
                logger.error(f"Failed to add unknown person with auto-generated ID: {unknown_id}")
                return None

        except Exception as e:
            logger.error(f"Error in add_unknown_person_auto_id: {str(e)}")
            return None

    def unknown_person_exists(self, unknown_id: str) -> bool:
        """
        Check if an unknown person exists in the face bank.

        Args:
            unknown_id: ID to check

        Returns:
            bool: True if person exists, False otherwise
        """
        if self.names is None or len(self.names) == 0:
            return False
        return unknown_id in self.names

    def clear_all_unknown_persons(self) -> bool:
        """
        Clear all unknown persons from the face bank.

        Returns:
            bool: True if clearing was successful, False otherwise
        """
        try:
            logger.info("Clearing all unknown persons from face bank")

            # Create backup before clearing
            self._create_backup()

            # Reset to empty state
            self.embeddings = None
            self.names = np.array([])

            # Save empty face bank
            if self._save_unknown_facebank():
                logger.info("Successfully cleared all unknown persons")
                return True
            else:
                logger.error("Failed to save empty face bank")
                return False

        except Exception as e:
            logger.error(f"Error clearing unknown persons: {str(e)}")
            return False

    def get_next_unknown_id(self) -> str:
        """
        Get the next available unknown person ID without adding anyone.

        Returns:
            str: Next available ID in format "Unknown_X"
        """
        return self._generate_next_unknown_id()

    def set_model_references(self, mtcnn, model):
        """
        Set model references for face processing.

        Args:
            mtcnn: MTCNN face detector instance
            model: FaceNet model instance
        """
        self.mtcnn = mtcnn
        self.model = model
        logger.info("Model references updated for UnknownFacebankManager")

    def reload_facebank(self) -> bool:
        """
        Reload the face bank from disk.

        Returns:
            bool: True if reload was successful, False otherwise
        """
        try:
            logger.info("Reloading unknown face bank from disk")
            self._load_unknown_facebank()
            return True
        except Exception as e:
            logger.error(f"Error reloading unknown face bank: {str(e)}")
            return False

    def validate_facebank_integrity(self) -> Dict[str, Any]:
        """
        Validate the integrity of the unknown face bank.

        Returns:
            Dict: Validation results with status and any issues found
        """
        validation_result = {
            'is_valid': True,
            'issues': [],
            'warnings': [],
            'info': {}
        }

        try:
            # Check if files exist
            if not os.path.exists(self.unknown_facebank_path):
                validation_result['issues'].append("Unknown facebank.pth file not found")
                validation_result['is_valid'] = False

            if not os.path.exists(self.unknown_names_path):
                validation_result['issues'].append("Unknown names.npy file not found")
                validation_result['is_valid'] = False

            # Check data consistency
            if self.embeddings is not None and self.names is not None:
                if self.embeddings.shape[0] != len(self.names):
                    validation_result['issues'].append(
                        f"Mismatch between embeddings ({self.embeddings.shape[0]}) and names ({len(self.names)})"
                    )
                    validation_result['is_valid'] = False

            # Check for empty face bank
            if (self.embeddings is None or self.embeddings.shape[0] == 0) and len(self.names) == 0:
                validation_result['warnings'].append("Face bank is empty")

            # Add info
            validation_result['info'] = self.get_facebank_info()

        except Exception as e:
            validation_result['issues'].append(f"Error during validation: {str(e)}")
            validation_result['is_valid'] = False

        return validation_result
