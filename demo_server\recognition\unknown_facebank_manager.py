import os
import logging
import torch
import numpy as np
from PIL import Image
from django.conf import settings

logger = logging.getLogger(__name__)


class UnknownFacebankManager:
    """
    Manages a separate face bank system specifically for unknown persons.
    This prevents unknown person IDs from interfering with the main face recognition system
    while maintaining proper records of detected unknown individuals.
    """
    
    def __init__(self, mtcnn=None, model=None):
        """
        Initialize the unknown facebank manager.
        
        Args:
            mtcnn: MTCNN instance for face alignment
            model: FaceNet model for embedding generation
        """
        self.conf = settings.RECOGNITION_CONFIG
        self.mtcnn = mtcnn
        self.model = model
        
        # Unknown facebank specific paths
        self.unknown_facebank_dir = os.path.join(self.conf['facebank_path'], 'unknown_facebank')
        self.unknown_facebank_path = os.path.join(self.unknown_facebank_dir, 'unknown_facebank.pth')
        self.unknown_names_path = os.path.join(self.unknown_facebank_dir, 'unknown_names.npy')
        
        # Create directory if it doesn't exist
        os.makedirs(self.unknown_facebank_dir, exist_ok=True)
        
        # Load existing unknown facebank
        self.unknown_embeddings = None
        self.unknown_names = None
        self._load_unknown_facebank()
        
        logger.info(f"UnknownFacebankManager initialized with {len(self.unknown_names) if self.unknown_names is not None else 0} unknown persons")
    
    def _load_unknown_facebank(self):
        """Load unknown facebank data from files"""
        try:
            if os.path.exists(self.unknown_facebank_path) and os.path.exists(self.unknown_names_path):
                self.unknown_embeddings = torch.load(
                    self.unknown_facebank_path, 
                    map_location=self.conf['device'], 
                    weights_only=True
                )
                self.unknown_names = np.load(self.unknown_names_path, allow_pickle=True)
                logger.info(f"Loaded unknown facebank with {len(self.unknown_names)} persons")
            else:
                # Initialize empty unknown facebank
                self._create_empty_unknown_facebank()
                logger.info("Created empty unknown facebank")
        except Exception as e:
            logger.error(f"Error loading unknown facebank: {str(e)}")
            self._create_empty_unknown_facebank()
    
    def _create_empty_unknown_facebank(self):
        """Create empty unknown facebank files"""
        try:
            # Create empty tensors and arrays
            self.unknown_embeddings = None
            self.unknown_names = np.array([])
            
            # Save empty files
            torch.save(self.unknown_embeddings, self.unknown_facebank_path)
            np.save(self.unknown_names_path, self.unknown_names)
            
            logger.info("Created empty unknown facebank files")
        except Exception as e:
            logger.error(f"Error creating empty unknown facebank: {str(e)}")
    
    def _save_unknown_facebank(self):
        """Save unknown facebank data to files"""
        try:
            torch.save(self.unknown_embeddings, self.unknown_facebank_path)
            np.save(self.unknown_names_path, self.unknown_names)
            logger.debug("Unknown facebank saved successfully")
            return True
        except Exception as e:
            logger.error(f"Error saving unknown facebank: {str(e)}")
            return False
    
    def get_next_unknown_id(self):
        """
        Get the next available unknown person ID.
        
        Returns:
            str: Next unknown person name (e.g., "Unknown_8")
        """
        try:
            if self.unknown_names is None or len(self.unknown_names) == 0:
                return "Unknown_1"
            
            # Find the highest existing number
            max_number = 0
            for name in self.unknown_names:
                if isinstance(name, str) and name.startswith("Unknown_"):
                    try:
                        number_part = name.split("_")[1]
                        number = int(number_part)
                        max_number = max(max_number, number)
                    except (ValueError, IndexError):
                        logger.warning(f"Malformed unknown name in facebank: {name}")
                        continue
            
            next_number = max_number + 1
            return f"Unknown_{next_number}"
            
        except Exception as e:
            logger.error(f"Error getting next unknown ID: {str(e)}")
            return "Unknown_1"
    
    def add_unknown_person(self, person_name, image_path):
        """
        Add a new unknown person to the unknown facebank.
        
        Args:
            person_name: Name of the unknown person (e.g., "Unknown_8")
            image_path: Path to the face image
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not self.mtcnn or not self.model:
                logger.error("MTCNN or model not available for embedding generation")
                return False
            
            # Load and process the image
            img = Image.open(image_path)
            
            # Align face if needed
            if img.size != (112, 112):
                try:
                    img = self.mtcnn.align(img, self.conf['min_face_size'])
                except Exception as e:
                    logger.error(f"Error aligning face for {person_name}: {str(e)}")
                    return False
            
            if img is None:
                logger.error(f"Face alignment failed for {person_name}")
                return False
            
            # Generate embedding
            with torch.no_grad():
                embedding = self.model(self.conf['test_transform'](img).to(self.conf['device']).unsqueeze(0))
            
            # Add to unknown facebank
            if self.unknown_embeddings is None:
                self.unknown_embeddings = embedding
                self.unknown_names = np.array([person_name])
            else:
                self.unknown_embeddings = torch.cat([self.unknown_embeddings, embedding])
                self.unknown_names = np.append(self.unknown_names, person_name)
            
            # Save updated unknown facebank
            if self._save_unknown_facebank():
                logger.info(f"Added {person_name} to unknown facebank")
                return True
            else:
                logger.error(f"Failed to save unknown facebank after adding {person_name}")
                return False
                
        except Exception as e:
            logger.error(f"Error adding unknown person {person_name}: {str(e)}")
            return False

    def remove_unknown_person(self, person_name):
        """
        Remove an unknown person from the unknown facebank.

        Args:
            person_name: Name of the unknown person to remove

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if self.unknown_names is None or len(self.unknown_names) == 0:
                logger.warning(f"Cannot remove {person_name}: unknown facebank is empty")
                return False

            # Find indices of the person to remove
            person_indices = [i for i, name in enumerate(self.unknown_names) if name == person_name]

            if not person_indices:
                logger.warning(f"Person {person_name} not found in unknown facebank")
                return False

            # Remove all instances of the person (reverse order to maintain indices)
            for idx in reversed(person_indices):
                if idx == 0:
                    if len(self.unknown_names) > 1:
                        self.unknown_embeddings = self.unknown_embeddings[1:]
                        self.unknown_names = self.unknown_names[1:]
                    else:
                        self.unknown_embeddings = None
                        self.unknown_names = np.array([])
                        break
                else:
                    self.unknown_embeddings = torch.cat([
                        self.unknown_embeddings[:idx],
                        self.unknown_embeddings[idx+1:]
                    ])
                    self.unknown_names = np.concatenate([
                        self.unknown_names[:idx],
                        self.unknown_names[idx+1:]
                    ])

            # Save updated unknown facebank
            if self._save_unknown_facebank():
                logger.info(f"Removed {len(person_indices)} instances of {person_name} from unknown facebank")
                return True
            else:
                logger.error(f"Failed to save unknown facebank after removing {person_name}")
                return False

        except Exception as e:
            logger.error(f"Error removing unknown person {person_name}: {str(e)}")
            return False

    def update_unknown_person(self, person_name, new_image_path):
        """
        Update an unknown person's embedding with a new image.

        Args:
            person_name: Name of the unknown person to update
            new_image_path: Path to the new face image

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Remove existing person
            if not self.remove_unknown_person(person_name):
                logger.warning(f"Could not remove existing {person_name} for update")

            # Add with new image
            return self.add_unknown_person(person_name, new_image_path)

        except Exception as e:
            logger.error(f"Error updating unknown person {person_name}: {str(e)}")
            return False

    def get_unknown_person_count(self):
        """
        Get the total number of unknown persons in the facebank.

        Returns:
            int: Number of unknown persons
        """
        if self.unknown_names is None:
            return 0
        return len(self.unknown_names)

    def get_unknown_person_names(self):
        """
        Get all unknown person names in the facebank.

        Returns:
            list: List of unknown person names
        """
        if self.unknown_names is None:
            return []
        return list(self.unknown_names)

    def rebuild_unknown_facebank_from_directories(self):
        """
        Rebuild the unknown facebank by scanning the unknowns directory structure.
        This method processes all Unknown_X directories and creates embeddings.

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not self.mtcnn or not self.model:
                logger.error("MTCNN or model not available for rebuilding unknown facebank")
                return False

            # Clear existing unknown facebank
            self.unknown_embeddings = None
            self.unknown_names = np.array([])

            # Scan unknowns directory
            unknowns_dir = os.path.join(self.conf['facebank_path'], 'unknowns')
            if not os.path.exists(unknowns_dir) or not os.path.isdir(unknowns_dir):
                logger.info("No unknowns directory found, creating empty unknown facebank")
                return self._save_unknown_facebank()

            embeddings = []
            names = []

            for path in os.scandir(unknowns_dir):
                if not path.is_dir():
                    continue

                if not path.name.startswith("Unknown_"):
                    continue

                # Process this unknown person directory
                embs = self._process_unknown_person_directory(path.path)

                if len(embs) == 0:
                    logger.warning(f"No embeddings found for unknown person {path.name}")
                    continue

                # Average all embeddings for this person
                embedding = torch.cat(embs).mean(0, keepdim=True)
                embeddings.append(embedding)
                names.append(path.name)
                logger.info(f'Added unknown person {path.name} to facebank')

            # Update unknown facebank
            if len(embeddings) > 0:
                self.unknown_embeddings = torch.cat(embeddings)
                self.unknown_names = np.array(names)
            else:
                self.unknown_embeddings = None
                self.unknown_names = np.array([])

            # Save updated unknown facebank
            if self._save_unknown_facebank():
                logger.info(f"Rebuilt unknown facebank with {len(names)} persons")
                return True
            else:
                logger.error("Failed to save rebuilt unknown facebank")
                return False

        except Exception as e:
            logger.error(f"Error rebuilding unknown facebank: {str(e)}")
            return False

    def _process_unknown_person_directory(self, directory_path):
        """
        Process a directory of unknown person face images and return embeddings.
        Based on the existing _process_person_directory method in facenet_recognizer.py

        Args:
            directory_path: Path to the unknown person directory

        Returns:
            list: List of embeddings for the person
        """
        embs = []
        try:
            for file in os.scandir(directory_path):
                if not file.is_file():
                    continue

                try:
                    img = Image.open(file.path)
                except Exception as e:
                    logger.warning(f"Error opening image {file.path}: {e}")
                    continue

                # Align face if needed
                if img.size != (112, 112):
                    try:
                        img = self.mtcnn.align(img, self.conf['min_face_size'])
                    except Exception as e:
                        logger.warning(f"Error aligning image {file.path}: {e}")
                        continue

                if img is None:
                    logger.warning(f"Alignment failed for image {file.path}")
                    continue

                # Generate embedding
                with torch.no_grad():
                    try:
                        emb = self.model(self.conf['test_transform'](img).to(self.conf['device']).unsqueeze(0))
                        embs.append(emb)
                    except Exception as e:
                        logger.warning(f"Error during model inference for image {file.path}: {e}")
                        continue

        except Exception as e:
            logger.error(f"Error scanning directory {directory_path}: {e}")

        return embs

    def get_facebank_info(self):
        """
        Get information about the unknown facebank.

        Returns:
            dict: Information about the unknown facebank
        """
        info = {
            'total_persons': self.get_unknown_person_count(),
            'person_names': self.get_unknown_person_names(),
            'facebank_path': self.unknown_facebank_path,
            'names_path': self.unknown_names_path,
            'embeddings_shape': None
        }

        if self.unknown_embeddings is not None:
            info['embeddings_shape'] = self.unknown_embeddings.shape

        return info
