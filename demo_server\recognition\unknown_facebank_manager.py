"""
Unknown Person Face Bank Manager

This module provides functionality to manage a separate face bank system specifically 
for unknown persons detected by the face recognition system. It maintains separate
unknown_facebank.pth and unknown_names.npy files to track unknown individuals
without interfering with the main face recognition system.

Key Features:
- Separate face bank for unknown persons
- Incremental naming (Unknown_1, Unknown_2, etc.)
- Face embedding storage and management
- Integration with existing face recognition patterns
- Proper error handling and logging

"""

import torch
import numpy as np
import os
import io
import logging
from PIL import Image
from django.conf import settings
from typing import Optional, Tuple, List, Dict, Any
import shutil
from datetime import datetime

logger = logging.getLogger(__name__)


class UnknownFacebankManager:
    """
    Manages a separate face bank system for unknown persons.
    
    This class handles the creation, loading, saving, and management of
    unknown person face embeddings and their associated names/IDs.
    """
    
    def __init__(self, mtcnn=None, model=None):
        """
        Initialize the Unknown Facebank Manager.
        
        Args:
            mtcnn: MTCNN face detector instance (optional)
            model: FaceNet model instance (optional)
        """
        logger.info("Initializing UnknownFacebankManager")
        
        # Get configuration from Django settings
        self.conf = settings.RECOGNITION_CONFIG
        
        # Set up paths for unknown person face bank
        self.unknown_facebank_dir = os.path.join(self.conf['facebank_path'], 'unknowns')
        self.unknown_facebank_path = os.path.join(self.unknown_facebank_dir, 'unknown_facebank.pth')
        self.unknown_names_path = os.path.join(self.unknown_facebank_dir, 'unknown_names.npy')
        
        # Ensure the unknowns directory exists
        os.makedirs(self.unknown_facebank_dir, exist_ok=True)
        
        # Store model references (will be injected from main recognizer)
        self.mtcnn = mtcnn
        self.model = model
        
        # Initialize face bank data
        self.embeddings = None
        self.names = None
        
        # Load existing unknown face bank
        self._load_unknown_facebank()
        
        logger.info("UnknownFacebankManager initialization complete")
    
    def _load_unknown_facebank(self):
        """
        Load existing unknown face bank data from files.
        
        If files don't exist, initialize empty face bank.
        """
        try:
            if (os.path.exists(self.unknown_facebank_path) and 
                os.path.exists(self.unknown_names_path)):
                
                logger.info("Loading existing unknown face bank")
                
                # Load embeddings
                self.embeddings = torch.load(
                    self.unknown_facebank_path, 
                    map_location=self.conf['device'], 
                    weights_only=True
                )
                
                # Load names
                self.names = np.load(self.unknown_names_path, allow_pickle=True)
                
                logger.info(f"Loaded unknown face bank with {len(self.names)} persons")
                
            else:
                logger.info("No existing unknown face bank found, initializing empty")
                self._initialize_empty_facebank()
                
        except Exception as e:
            logger.error(f"Error loading unknown face bank: {str(e)}")
            logger.info("Initializing empty face bank due to loading error")
            self._initialize_empty_facebank()
    
    def _initialize_empty_facebank(self):
        """Initialize empty face bank data structures."""
        self.embeddings = None
        self.names = np.array([])
        self._save_unknown_facebank()
        logger.info("Initialized empty unknown face bank")
    
    def _save_unknown_facebank(self):
        """
        Save current face bank data to files.
        
        Returns:
            bool: True if save was successful, False otherwise
        """
        try:
            # Create backup of existing files if they exist
            self._create_backup()
            
            # Save embeddings
            torch.save(self.embeddings, self.unknown_facebank_path)
            
            # Save names
            np.save(self.unknown_names_path, self.names)
            
            logger.info("Successfully saved unknown face bank")
            return True
            
        except Exception as e:
            logger.error(f"Error saving unknown face bank: {str(e)}")
            return False
    
    def _create_backup(self):
        """Create backup of existing face bank files."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        if os.path.exists(self.unknown_facebank_path):
            backup_path = f"{self.unknown_facebank_path}.backup_{timestamp}"
            shutil.copy2(self.unknown_facebank_path, backup_path)

        if os.path.exists(self.unknown_names_path):
            backup_path = f"{self.unknown_names_path}.backup_{timestamp}"
            shutil.copy2(self.unknown_names_path, backup_path)

    def _generate_next_unknown_id(self) -> str:
        """
        Generate the next available unknown person ID.

        Returns:
            str: Next available ID in format "Unknown_X"
        """
        if self.names is None or len(self.names) == 0:
            return "Unknown_1"

        # Find the highest existing number
        max_num = 0
        for name in self.names:
            if name.startswith("Unknown_"):
                try:
                    num = int(name.split("_")[1])
                    max_num = max(max_num, num)
                except (IndexError, ValueError):
                    continue

        return f"Unknown_{max_num + 1}"

    def _process_face_image(self, image_path: str) -> Optional[torch.Tensor]:
        """
        Process a face image and generate its embedding.

        Args:
            image_path: Path to the face image

        Returns:
            torch.Tensor: Face embedding or None if processing failed
        """
        if self.model is None or self.mtcnn is None:
            logger.error("Model or MTCNN not available for face processing")
            return None

        try:
            # Load and process image
            img = Image.open(image_path)

            # Align face if needed
            if img.size != (112, 112):
                try:
                    img = self.mtcnn.align(img, self.conf['min_face_size'])
                except Exception as e:
                    logger.error(f"Error aligning face image {image_path}: {str(e)}")
                    return None

            if img is None:
                logger.error(f"Face alignment failed for {image_path}")
                return None

            # Generate embedding
            with torch.no_grad():
                embedding = self.model(
                    self.conf['test_transform'](img).to(self.conf['device']).unsqueeze(0)
                )

            logger.info(f"Successfully generated embedding for {image_path}")
            return embedding

        except Exception as e:
            logger.error(f"Error processing face image {image_path}: {str(e)}")
            return None

    def add_unknown_person(self, unknown_id: str, image_path: str) -> bool:
        """
        Add a new unknown person to the face bank.

        Args:
            unknown_id: ID for the unknown person (e.g., "Unknown_1")
            image_path: Path to the face image

        Returns:
            bool: True if addition was successful, False otherwise
        """
        try:
            logger.info(f"Adding unknown person {unknown_id} from {image_path}")

            # Check if ID already exists
            if self.names is not None and unknown_id in self.names:
                logger.warning(f"Unknown person {unknown_id} already exists in face bank")
                return False

            # Process the face image
            embedding = self._process_face_image(image_path)
            if embedding is None:
                logger.error(f"Failed to process face image for {unknown_id}")
                return False

            # Add to face bank
            if self.embeddings is None:
                self.embeddings = embedding
                self.names = np.array([unknown_id])
            else:
                self.embeddings = torch.cat([self.embeddings, embedding])
                self.names = np.append(self.names, unknown_id)

            # Save updated face bank
            if self._save_unknown_facebank():
                logger.info(f"Successfully added unknown person {unknown_id}")
                return True
            else:
                logger.error(f"Failed to save face bank after adding {unknown_id}")
                return False

        except Exception as e:
            logger.error(f"Error adding unknown person {unknown_id}: {str(e)}")
            return False

    def remove_unknown_person(self, unknown_id: str) -> bool:
        """
        Remove an unknown person from the face bank.

        Args:
            unknown_id: ID of the unknown person to remove

        Returns:
            bool: True if removal was successful, False otherwise
        """
        try:
            logger.info(f"Removing unknown person {unknown_id}")

            if self.names is None or len(self.names) == 0:
                logger.warning("No unknown persons in face bank to remove")
                return False

            # Find indices of the person to remove
            person_indices = [i for i, name in enumerate(self.names) if name == unknown_id]

            if not person_indices:
                logger.warning(f"Unknown person {unknown_id} not found in face bank")
                return False

            # Remove all instances (reverse order to maintain indices)
            for idx in reversed(person_indices):
                if idx == 0:
                    if len(self.names) > 1:
                        self.embeddings = self.embeddings[1:]
                        self.names = self.names[1:]
                    else:
                        # Last person, reset to empty
                        self.embeddings = None
                        self.names = np.array([])
                        break
                else:
                    self.embeddings = torch.cat([self.embeddings[:idx], self.embeddings[idx+1:]])
                    self.names = np.concatenate([self.names[:idx], self.names[idx+1:]])

            # Save updated face bank
            if self._save_unknown_facebank():
                logger.info(f"Successfully removed {len(person_indices)} instances of {unknown_id}")
                return True
            else:
                logger.error(f"Failed to save face bank after removing {unknown_id}")
                return False

        except Exception as e:
            logger.error(f"Error removing unknown person {unknown_id}: {str(e)}")
            return False


    def get_next_unknown_id(self) -> str:
        """
        Get the next available unknown person ID without adding anyone.

        Returns:
            str: Next available ID in format "Unknown_X"
        """
        return self._generate_next_unknown_id()
