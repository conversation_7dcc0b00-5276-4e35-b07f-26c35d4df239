#!/usr/bin/env python3
"""
Test script for Unknown Facebank Manager

This script demonstrates and tests the functionality of the UnknownFacebankManager
class, including adding, removing, and querying unknown persons.

Usage:
    python test_unknown_facebank_manager.py

Author: Augment Agent
Created: 2025-09-11
"""

import os
import sys
import django
from pathlib import Path

# Add the demo_server directory to Python path
demo_server_path = Path(__file__).parent
sys.path.insert(0, str(demo_server_path))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'web_server.settings')
django.setup()

from recognition.unknown_facebank_manager import UnknownFacebankManager
from recognition.facenet_recognizer import FaceNetRecognizer
import glob


def test_unknown_facebank_manager():
    """Test the UnknownFacebankManager functionality."""
    
    print("🧪 Testing Unknown Facebank Manager")
    print("=" * 50)
    
    try:
        # Initialize the main recognizer to get model references
        print("📋 Initializing FaceNet recognizer...")
        facenet_recognizer = FaceNetRecognizer()
        
        # Initialize unknown facebank manager with model references
        print("📋 Initializing Unknown Facebank Manager...")
        unknown_manager = UnknownFacebankManager(
            mtcnn=facenet_recognizer.mtcnn,
            model=facenet_recognizer.model
        )
        
        print("✅ Initialization successful")
        
        # Test 1: Get initial face bank info
        print("\n🔍 Test 1: Getting initial face bank info")
        info = unknown_manager.get_facebank_info()
        print(f"   Total persons: {info['total_persons']}")
        print(f"   Total embeddings: {info['total_embeddings']}")
        print(f"   Person names: {info['person_names']}")
        print(f"   Facebank exists: {info['facebank_exists']}")
        print(f"   Names file exists: {info['names_exists']}")
        
        # Test 2: Validate face bank integrity
        print("\n🔍 Test 2: Validating face bank integrity")
        validation = unknown_manager.validate_facebank_integrity()
        print(f"   Is valid: {validation['is_valid']}")
        if validation['issues']:
            print(f"   Issues: {validation['issues']}")
        if validation['warnings']:
            print(f"   Warnings: {validation['warnings']}")
        
        # Test 3: Get next unknown ID
        print("\n🔍 Test 3: Getting next unknown ID")
        next_id = unknown_manager.get_next_unknown_id()
        print(f"   Next ID: {next_id}")
        
        # Test 4: Check if we have test images
        print("\n🔍 Test 4: Looking for test images")
        test_image_paths = []
        
        # Look for existing unknown person images
        unknowns_dir = "./media/alert_photos/unknowns"
        if os.path.exists(unknowns_dir):
            for unknown_dir in os.listdir(unknowns_dir):
                unknown_path = os.path.join(unknowns_dir, unknown_dir)
                if os.path.isdir(unknown_path):
                    # Look for image files in this directory
                    for ext in ['*.jpg', '*.jpeg', '*.png']:
                        images = glob.glob(os.path.join(unknown_path, ext))
                        test_image_paths.extend(images)
        
        print(f"   Found {len(test_image_paths)} test images")
        
        # Test 5: Add unknown person (if we have test images)
        if test_image_paths:
            print("\n🔍 Test 5: Adding unknown person")
            test_image_path = test_image_paths[0]
            print(f"   Using test image: {test_image_path}")
            
            # Add the unknown person
            success = unknown_manager.add_unknown_person(next_id, test_image_path)
            if success:
                print("   ✅ Successfully added unknown person to facebank")
                
                # Verify it was added
                info = unknown_manager.get_facebank_info()
                print(f"   Updated total persons: {info['total_persons']}")
                print(f"   Updated person names: {info['person_names']}")
                
                # Test 6: Check if person exists
                print("\n🔍 Test 6: Checking if person exists")
                exists = unknown_manager.unknown_person_exists(next_id)
                print(f"   Person {next_id} exists: {exists}")
                
                # Test 7: Remove the person
                print("\n🔍 Test 7: Removing unknown person")
                remove_success = unknown_manager.remove_unknown_person(next_id)
                if remove_success:
                    print("   ✅ Successfully removed unknown person from facebank")
                    
                    # Verify removal
                    info = unknown_manager.get_facebank_info()
                    print(f"   Updated total persons: {info['total_persons']}")
                    print(f"   Updated person names: {info['person_names']}")
                else:
                    print("   ❌ Failed to remove unknown person")
            else:
                print("   ❌ Failed to add unknown person to facebank")
        else:
            print("   ⚠️ No test images found, skipping addition/removal tests")
        
        # Test 8: Test auto-ID generation
        if test_image_paths:
            print("\n🔍 Test 8: Testing auto-ID generation")
            auto_id = unknown_manager.add_unknown_person_auto_id(test_image_paths[0])
            if auto_id:
                print(f"   ✅ Successfully added with auto-generated ID: {auto_id}")
                
                # Clean up
                unknown_manager.remove_unknown_person(auto_id)
                print(f"   🧹 Cleaned up test person: {auto_id}")
            else:
                print("   ❌ Failed to add with auto-generated ID")
        
        # Test 9: Final face bank info
        print("\n🔍 Test 9: Final face bank info")
        final_info = unknown_manager.get_facebank_info()
        print(f"   Final total persons: {final_info['total_persons']}")
        print(f"   Final person names: {final_info['person_names']}")
        
        print("\n✅ All tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_unknown_facebank_manager()
    if success:
        print("\n🎉 Unknown Facebank Manager is working correctly!")
    else:
        print("\n💥 Unknown Facebank Manager tests failed!")
        sys.exit(1)
