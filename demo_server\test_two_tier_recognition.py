#!/usr/bin/env python3
"""
Test script for Two-Tier Recognition System

This script tests the integrated two-tier face recognition system that includes:
1. Main face bank checking (known persons)
2. Unknown face bank checking (existing unknown persons)
3. New unknown person creation

Usage:
    python test_two_tier_recognition.py

Author: Augment Agent
Created: 2025-09-11
"""

import os
import sys
import django
import logging
import cv2
import numpy as np
from pathlib import Path

# Add the demo_server directory to Python path
demo_server_path = Path(__file__).parent
sys.path.insert(0, str(demo_server_path))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'web_server.settings')
django.setup()

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

from recognition.facenet_recognizer import FaceNetRecognizer
from PIL import Image
import glob


def test_two_tier_recognition():
    """Test the two-tier recognition system comprehensively."""
    
    print("🧪 Testing Two-Tier Recognition System")
    print("=" * 60)
    
    try:
        # Initialize the recognizer (which now includes unknown face bank)
        print("📋 Initializing FaceNet recognizer with two-tier system...")
        recognizer = FaceNetRecognizer()
        print("✅ Recognizer initialized successfully")
        
        # Test 1: Check initial state
        print("\n🔍 Test 1: Checking initial system state")
        
        # Check main face bank
        main_facebank_info = {
            'embeddings_available': recognizer.embeddings is not None,
            'num_embeddings': len(recognizer.embeddings) if recognizer.embeddings is not None else 0,
            'num_names': len(recognizer.names) if recognizer.names is not None else 0,
            'names': list(recognizer.names) if recognizer.names is not None else []
        }
        
        print(f"   Main Face Bank:")
        print(f"     Embeddings available: {main_facebank_info['embeddings_available']}")
        print(f"     Number of embeddings: {main_facebank_info['num_embeddings']}")
        print(f"     Number of names: {main_facebank_info['num_names']}")
        print(f"     Names: {main_facebank_info['names']}")
        
        # Check unknown face bank
        unknown_info = recognizer.unknown_manager.get_facebank_info()
        print(f"   Unknown Face Bank:")
        print(f"     Total persons: {unknown_info['total_persons']}")
        print(f"     Person names: {unknown_info['person_names']}")
        print(f"     Facebank exists: {unknown_info['facebank_exists']}")
        
        # Test 2: Configuration check
        print("\n🔍 Test 2: Checking configuration")
        config = recognizer.conf
        print(f"   Unknown threshold: {config.get('unknown_threshold', 'Not set')}")
        print(f"   Enable unknown facebank: {config.get('enable_unknown_facebank', 'Not set')}")
        print(f"   Unknown similarity threshold: {config.get('unknown_similarity_threshold', 'Not set')}")
        
        # Test 3: Look for test images
        print("\n🔍 Test 3: Looking for test images")
        test_images = []
        
        # Look for known person images
        known_persons_dir = "./media/alert_photos"
        if os.path.exists(known_persons_dir):
            for person_dir in os.listdir(known_persons_dir):
                person_path = os.path.join(known_persons_dir, person_dir)
                if os.path.isdir(person_path) and person_dir not in ['unknowns', 'temp']:
                    for ext in ['*.jpg', '*.jpeg', '*.png']:
                        images = glob.glob(os.path.join(person_path, ext))
                        test_images.extend([(img, 'known', person_dir) for img in images])
        
        # Look for unknown person images
        unknowns_dir = "./media/alert_photos/unknowns"
        if os.path.exists(unknowns_dir):
            for unknown_dir in os.listdir(unknowns_dir):
                unknown_path = os.path.join(unknowns_dir, unknown_dir)
                if os.path.isdir(unknown_path):
                    for ext in ['*.jpg', '*.jpeg', '*.png']:
                        images = glob.glob(os.path.join(unknown_path, ext))
                        test_images.extend([(img, 'unknown', unknown_dir) for img in images])
        
        print(f"   Found {len(test_images)} test images")
        
        # Test 4: Test recognition on sample images
        if test_images:
            print("\n🔍 Test 4: Testing recognition on sample images")
            
            # Test with first few images
            test_count = min(3, len(test_images))
            
            for i, (image_path, expected_type, expected_name) in enumerate(test_images[:test_count]):
                print(f"\n   Testing image {i+1}: {image_path}")
                print(f"     Expected type: {expected_type}, Expected name: {expected_name}")
                
                try:
                    # Load image as frame (BGR format for OpenCV)
                    frame = cv2.imread(image_path)
                    if frame is None:
                        print(f"     ❌ Could not load image: {image_path}")
                        continue
                    
                    # Perform recognition
                    bboxes, names, confidences = recognizer.recognize(frame, camera_id=f"test_{i}")
                    
                    print(f"     Results:")
                    print(f"       Detected faces: {len(names)}")
                    
                    for j, (name, conf) in enumerate(zip(names, confidences)):
                        print(f"       Face {j+1}: {name} (confidence: {conf:.3f})")
                        
                        # Analyze result
                        if expected_type == 'known':
                            if name == expected_name:
                                print(f"         ✅ Correctly identified known person")
                            elif name.startswith('Unknown_'):
                                print(f"         ⚠️ Known person identified as unknown")
                            else:
                                print(f"         ❌ Misidentified as different person")
                        elif expected_type == 'unknown':
                            if name.startswith('Unknown_'):
                                print(f"         ✅ Correctly identified as unknown person")
                            else:
                                print(f"         ❌ Unknown person misidentified as known")
                
                except Exception as e:
                    print(f"     ❌ Error during recognition: {str(e)}")
        
        # Test 5: Check final unknown face bank state
        print("\n🔍 Test 5: Checking final unknown face bank state")
        final_unknown_info = recognizer.unknown_manager.get_facebank_info()
        print(f"   Final total unknown persons: {final_unknown_info['total_persons']}")
        print(f"   Final person names: {final_unknown_info['person_names']}")
        
        # Test 6: Validate face bank integrity
        print("\n🔍 Test 6: Validating face bank integrity")
        validation = recognizer.unknown_manager.validate_facebank_integrity()
        print(f"   Unknown face bank is valid: {validation['is_valid']}")
        if validation['issues']:
            print(f"   Issues found: {validation['issues']}")
        if validation['warnings']:
            print(f"   Warnings: {validation['warnings']}")
        
        print("\n✅ Two-tier recognition system test completed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_backward_compatibility():
    """Test that the system maintains backward compatibility."""
    
    print("\n🔄 Testing Backward Compatibility")
    print("=" * 40)
    
    try:
        # Test that existing recognition calls still work
        recognizer = FaceNetRecognizer()
        
        # Create a simple test frame
        test_frame = np.zeros((480, 640, 3), dtype=np.uint8)
        
        # This should not crash and should return expected format
        bboxes, names, confidences = recognizer.recognize(test_frame)
        
        print(f"✅ Backward compatibility test passed")
        print(f"   Returned types: bboxes={type(bboxes)}, names={type(names)}, confidences={type(confidences)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Backward compatibility test failed: {str(e)}")
        return False


if __name__ == "__main__":
    print("🚀 Starting Two-Tier Recognition System Tests")
    print("=" * 60)
    
    # Run main test
    main_test_success = test_two_tier_recognition()
    
    # Run backward compatibility test
    compat_test_success = test_backward_compatibility()
    
    if main_test_success and compat_test_success:
        print("\n🎉 All tests passed! Two-tier recognition system is working correctly!")
    else:
        print("\n💥 Some tests failed! Please check the implementation.")
        sys.exit(1)
