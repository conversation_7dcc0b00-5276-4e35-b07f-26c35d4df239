# Unknown Person Face Bank Management System

## Overview

This implementation provides a comprehensive solution for managing unknown persons detected by the face recognition system. It creates a separate face bank system specifically for unknown individuals, preventing rapid ID incrementation and maintaining proper records.

## Key Features

- **Separate Face Bank**: Maintains `unknown_facebank.pth` and `unknown_names.npy` files specifically for unknown persons
- **Incremental Naming**: Automatically generates IDs like "Unknown_1", "Unknown_2", etc.
- **Model Integration**: Seamlessly integrates with existing FaceNet recognition system
- **Data Persistence**: Proper saving/loading with backup functionality
- **Error Handling**: Comprehensive error handling and logging
- **Management Interface**: Full CRUD operations for unknown persons

## Files Created

### Core Implementation
- `demo_server/recognition/unknown_facebank_manager.py` - Main implementation class
- `demo_server/test_unknown_facebank_manager.py` - Test script for functionality verification
- `demo_server/unknown_integration_example.py` - Integration example with main recognition system

### Data Files (Created automatically)
- `demo_server/media/alert_photos/unknowns/unknown_facebank.pth` - Face embeddings for unknown persons
- `demo_server/media/alert_photos/unknowns/unknown_names.npy` - Names/IDs for unknown persons

## Class: UnknownFacebankManager

### Initialization
```python
from recognition.unknown_facebank_manager import UnknownFacebankManager
from recognition.facenet_recognizer import FaceNetRecognizer

# Initialize main recognizer
main_recognizer = FaceNetRecognizer()

# Initialize unknown manager with model references
unknown_manager = UnknownFacebankManager(
    mtcnn=main_recognizer.mtcnn,
    model=main_recognizer.model
)
```

### Core Methods

#### Adding Unknown Persons
```python
# Add with specific ID
success = unknown_manager.add_unknown_person("Unknown_1", "/path/to/face/image.jpg")

# Add with auto-generated ID
unknown_id = unknown_manager.add_unknown_person_auto_id("/path/to/face/image.jpg")
```

#### Removing Unknown Persons
```python
# Remove specific person
success = unknown_manager.remove_unknown_person("Unknown_1")

# Clear all unknown persons
success = unknown_manager.clear_all_unknown_persons()
```

#### Querying Information
```python
# Get face bank information
info = unknown_manager.get_facebank_info()
# Returns: total_embeddings, total_persons, person_names, etc.

# Check if person exists
exists = unknown_manager.unknown_person_exists("Unknown_1")

# Get next available ID
next_id = unknown_manager.get_next_unknown_id()

# Get all unknown person names
names = unknown_manager.get_unknown_person_names()
```

#### Validation and Maintenance
```python
# Validate face bank integrity
validation = unknown_manager.validate_facebank_integrity()

# Reload from disk
success = unknown_manager.reload_facebank()
```

## Integration with Existing System

### Basic Integration Pattern
```python
class IntegratedRecognitionSystem:
    def __init__(self):
        self.main_recognizer = FaceNetRecognizer()
        self.unknown_manager = UnknownFacebankManager(
            mtcnn=self.main_recognizer.mtcnn,
            model=self.main_recognizer.model
        )
    
    def recognize_with_unknown_handling(self, image_path):
        # Perform main recognition
        bboxes, names, scores = self.main_recognizer.recognize(image)
        
        # Handle unknown persons
        for i, name in enumerate(names):
            if name == "Unknown":
                unknown_id = self.unknown_manager.add_unknown_person_auto_id(image_path)
                # Update name in results
                names[i] = unknown_id
        
        return bboxes, names, scores
```

## Benefits

1. **Prevents ID Inflation**: Unknown persons are properly tracked instead of generating new IDs constantly
2. **Data Persistence**: Unknown persons are saved and can be retrieved later
3. **Modular Design**: Separate from main face bank, doesn't interfere with known persons
4. **Scalable**: Can handle large numbers of unknown persons efficiently
5. **Maintainable**: Clean interface for management operations

## Testing

Run the test script to verify functionality:
```bash
cd demo_server
python test_unknown_facebank_manager.py
```

Run the integration example:
```bash
cd demo_server
python unknown_integration_example.py
```

## Configuration

The system uses the existing `RECOGNITION_CONFIG` from Django settings:
- `facebank_path`: Base path for face bank files
- `device`: CUDA/CPU device for model operations
- `test_transform`: Image preprocessing transforms

## Error Handling

The implementation includes comprehensive error handling:
- File I/O errors
- Model processing errors
- Data consistency validation
- Backup creation before modifications

## Future Enhancements

Potential improvements for the system:
1. **Face Similarity Checking**: Before adding new unknown person, check similarity with existing unknowns
2. **Automatic Cleanup**: Periodic cleanup of old unknown persons
3. **Database Integration**: Store unknown person metadata in database
4. **Face Quality Assessment**: Only save high-quality face images
5. **Clustering**: Group similar unknown faces together

## Troubleshooting

### Common Issues

1. **Model References Not Set**: Ensure MTCNN and model are properly injected
2. **File Permissions**: Check write permissions for the unknowns directory
3. **CUDA Errors**: Verify device configuration matches available hardware
4. **Memory Issues**: Monitor memory usage with large face banks

### Validation

Use the validation method to check system health:
```python
validation = unknown_manager.validate_facebank_integrity()
if not validation['is_valid']:
    print("Issues found:", validation['issues'])
```

## Support

For issues or questions about the unknown person face bank system, check:
1. Log files for error messages
2. Validation results for data integrity
3. Test scripts for functionality verification
