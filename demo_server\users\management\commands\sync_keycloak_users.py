"""
Django management command to sync users from Keycloak
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.conf import settings
from users.models import UserProfile
from users.auth_backends import KeycloakOIDCAuthenticationBackend
import requests
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Sync users and roles from Keycloak'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force sync even for recently synced users',
        )

    def handle(self, *args, **options):
        self.dry_run = options['dry_run']
        self.force = options['force']
        
        if self.dry_run:
            self.stdout.write("Running in dry-run mode - no changes will be made")
        
        try:
            backend = KeycloakOIDCAuthenticationBackend()
            admin_token = backend.get_admin_token()
            
            if not admin_token:
                self.stderr.write("Failed to get admin token from Keycloak")
                return
            
            users = self.get_keycloak_users(admin_token)
            
            for user_data in users:
                self.sync_user(user_data, admin_token)
                
            self.stdout.write(
                self.style.SUCCESS(f'Successfully synced {len(users)} users from Keycloak')
            )
            
        except Exception as e:
            self.stderr.write(f"Error syncing users: {str(e)}")

    def get_keycloak_users(self, admin_token):
        """Get all users from Keycloak"""
        try:
            headers = {
                'Authorization': f'Bearer {admin_token}',
                'Content-Type': 'application/json'
            }
            
            url = f"{settings.KEYCLOAK_SERVER_URL}/admin/realms/{settings.KEYCLOAK_REALM}/users"
            response = requests.get(url, headers=headers)
            
            if response.status_code == 200:
                return response.json()
            else:
                self.stderr.write(f"Failed to fetch users: {response.status_code}")
                return []
                
        except Exception as e:
            self.stderr.write(f"Error fetching users: {str(e)}")
            return []

    def sync_user(self, user_data, admin_token):
        """Sync a single user from Keycloak"""
        try:
            username = user_data.get('username')
            email = user_data.get('email', '')
            
            if not username:
                return
            
            # Get user roles
            roles_dict = self.get_user_roles(user_data['id'], admin_token)
            
            if self.dry_run:
                realm_roles = roles_dict.get('realm_roles', [])
                client_roles = roles_dict.get('client_roles', [])
                self.stdout.write(f"Would sync user: {username} with realm roles: {realm_roles}, client roles: {client_roles}")
                return
            
            # Find or create Django user
            user, created = User.objects.get_or_create(
                username=username,
                defaults={
                    'email': email,
                    'first_name': user_data.get('firstName', ''),
                    'last_name': user_data.get('lastName', ''),
                    'is_active': user_data.get('enabled', True)
                }
            )
            
            # Update user profile
            profile, _ = UserProfile.objects.get_or_create(
                user=user,
                defaults={'preferred_recognizer': 'facenet'}
            )
            
            # Update from Keycloak
            profile.update_from_keycloak(user_data, roles_dict)
            
            action = "Created" if created else "Updated"
            self.stdout.write(f"{action} user: {username}")
            
        except Exception as e:
            self.stderr.write(f"Error syncing user {username}: {str(e)}")

    def get_user_roles(self, user_id, admin_token):
        """Get realm and client roles for a specific user"""
        try:
            headers = {
                'Authorization': f'Bearer {admin_token}',
                'Content-Type': 'application/json'
            }
            
            # Get realm roles
            realm_url = f"{settings.KEYCLOAK_SERVER_URL}/admin/realms/{settings.KEYCLOAK_REALM}/users/{user_id}/role-mappings/realm"
            realm_response = requests.get(realm_url, headers=headers)
            
            realm_roles = []
            if realm_response.status_code == 200:
                roles_data = realm_response.json()
                realm_roles = [role['name'] for role in roles_data if role['name'] in settings.KEYCLOAK_REALM_ROLES]
            
            # Get client roles
            client_roles = []
            # First get the client internal ID
            clients_url = f"{settings.KEYCLOAK_SERVER_URL}/admin/realms/{settings.KEYCLOAK_REALM}/clients"
            clients_response = requests.get(clients_url, headers=headers, params={'clientId': settings.KEYCLOAK_CLIENT_ID})
            
            if clients_response.status_code == 200:
                clients_data = clients_response.json()
                if clients_data:
                    client_uuid = clients_data[0]['id']
                    
                    # Get client roles for user
                    client_roles_url = f"{settings.KEYCLOAK_SERVER_URL}/admin/realms/{settings.KEYCLOAK_REALM}/users/{user_id}/role-mappings/clients/{client_uuid}"
                    client_response = requests.get(client_roles_url, headers=headers)
                    
                    if client_response.status_code == 200:
                        client_roles_data = client_response.json()
                        client_roles = [role['name'] for role in client_roles_data if role['name'] in settings.KEYCLOAK_CLIENT_ROLES]
            
            return {
                'realm_roles': realm_roles,
                'client_roles': client_roles
            }
            
        except Exception as e:
            logger.error(f"Error getting user roles: {str(e)}")
            return {'realm_roles': [], 'client_roles': []}