import os
import uuid
import logging
from django.conf import settings
from django.db import transaction
from django.core.files.base import ContentFile
from django.core.cache import cache
from django.utils import timezone
from asgiref.sync import sync_to_async
import cv2

# Import models
from alerts.models import AlertPhoto, Alarm, AlertPerson

logger = logging.getLogger(__name__)


class AlertService:
    """Service for handling alert and photo operations"""
    
    @staticmethod
    async def handle_multiple_faces(frame, camera, names, confidences):
        """
        Handle multiple face detection by creating a single photo
        Only triggers if ALL detected faces are KNOWN persons

        Args:
            frame: Video frame containing multiple faces
            camera: Camera object
            names: List of detected person names
            confidences: List of confidence scores for each person

        Returns:
            AlertPhoto instance or None
        """
        try:
            # Must have at least 2 faces
            if len(names) <= 1:
                return None

            # ALL faces must be known (no "Unknown" persons)
            if any("Unknown" in name for name in names):
                logger.debug(f"Multiple face skipped: Unknown person detected in {names}")
                return None

            logger.info(f"Multiple KNOWN faces detected: {names}")
            
            # Check cooldown to prevent spam
            cache_key = f"multi_alert_cooldown_{camera.id}"
            last_alert_time = cache.get(cache_key)
            current_time = timezone.now()
            
            # Only create photo once per minute
            if last_alert_time and (current_time - last_alert_time).total_seconds() < 60:
                logger.debug(f"Multiple face cooldown active for camera {camera.id}")
                return None
            
            # Create multiple face photo and alarms
            alert_photo, alarm_count = await sync_to_async(AlertService._create_multiple_face_photo_and_alarms)(
                frame, camera, names, confidences
            )

            # Set cooldown
            cache.set(cache_key, current_time, 60)

            logger.info(f"Multiple faces photo created with {alarm_count} alarms for camera {camera.name}")
            return alert_photo
            
        except Exception as e:
            logger.error(f"Error handling multiple faces: {str(e)}")
            return None
    
    @staticmethod
    def _create_multiple_face_photo_and_alarms(frame, camera, names, confidences):
        """
        Create multiple face photo and corresponding alarms for Times tracking

        Args:
            frame: Video frame
            camera: Camera object
            names: List of detected person names
            confidences: List of confidence scores for each person

        Returns:
            tuple: (AlertPhoto instance, alarm_count)
        """
        try:
            filename = f"multiple_faces_{uuid.uuid4()}.jpg"
            os.makedirs(os.path.join(settings.MEDIA_ROOT, 'alert_photos', 'multiple_faces'), exist_ok=True)

            # Encode frame to JPEG
            _, buffer = cv2.imencode('.jpg', frame)
            content = ContentFile(buffer.tobytes())

            with transaction.atomic():
                # Create shared photo for multiple faces
                alert_photo = AlertPhoto.objects.create(
                    is_multiple_face=True,
                    person=None  # No specific person for multiple faces
                )
                alert_photo.photo.save(filename, content)

                # Create Alarm entries for each known person (for Times tracking)
                alarm_count = 0
                for name, confidence in zip(names, confidences):
                    if "Unknown" not in name:  # Only for known persons
                        try:
                            person = AlertPerson.objects.filter(name=name).first()
                            if person:
                                # Create alarm with shared photo and real confidence
                                Alarm.objects.create(
                                    person=person,
                                    camera=camera,
                                    confidence=confidence,  # Real confidence for each person
                                    alert_photo=alert_photo  # Shared photo
                                    # No video_snapshot - using shared photo
                                )
                                alarm_count += 1
                                logger.debug(f"Created alarm for {name} (confidence: {confidence:.2f}) in multiple face detection")
                        except Exception as e:
                            logger.error(f"Error creating alarm for {name}: {str(e)}")

            return alert_photo, alarm_count

        except Exception as e:
            logger.error(f"Error creating multiple face photo and alarms: {str(e)}")
            raise
