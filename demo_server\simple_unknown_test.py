#!/usr/bin/env python3
"""
Simple test for Unknown Facebank Manager - Basic functionality check
"""

import os
import sys
import django
import logging
from pathlib import Path

# Add the demo_server directory to Python path
demo_server_path = Path(__file__).parent
sys.path.insert(0, str(demo_server_path))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'web_server.settings')
django.setup()

# Setup logging
logging.basicConfig(level=logging.INFO)

print("🧪 Simple Unknown Facebank Manager Test")
print("=" * 40)

try:
    from recognition.unknown_facebank_manager import UnknownFacebankManager
    print("✅ Successfully imported UnknownFacebankManager")
    
    # Test basic initialization without model references
    print("📋 Testing basic initialization...")
    unknown_manager = UnknownFacebankManager()
    print("✅ Basic initialization successful")
    
    # Test getting next ID
    print("🔍 Testing next ID generation...")
    next_id = unknown_manager.get_next_unknown_id()
    print(f"✅ Next ID: {next_id}")
    
    # Test face bank info
    print("📊 Testing face bank info...")
    info = unknown_manager.get_facebank_info()
    print(f"✅ Face bank info retrieved:")
    print(f"   Total persons: {info['total_persons']}")
    print(f"   Person names: {info['person_names']}")
    print(f"   Facebank exists: {info['facebank_exists']}")
    
    # Test validation
    print("🔍 Testing validation...")
    validation = unknown_manager.validate_facebank_integrity()
    print(f"✅ Validation complete:")
    print(f"   Is valid: {validation['is_valid']}")
    if validation['issues']:
        print(f"   Issues: {validation['issues']}")
    if validation['warnings']:
        print(f"   Warnings: {validation['warnings']}")
    
    print("\n🎉 All basic tests passed!")
    
except Exception as e:
    print(f"❌ Test failed: {str(e)}")
    import traceback
    traceback.print_exc()
