#!/usr/bin/env python3
"""
Simple test for unknown facebank functionality
"""

import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'web_server.settings')
django.setup()

print("🚀 Starting Simple Unknown Facebank Test")

try:
    print("📦 Importing UnknownFacebankManager...")
    from recognition.unknown_facebank_manager import UnknownFacebankManager
    print("✅ Import successful")
    
    print("🔧 Initializing UnknownFacebankManager...")
    manager = UnknownFacebankManager()
    print("✅ Initialization successful")
    
    print("🔢 Testing ID generation...")
    next_id = manager.get_next_unknown_id()
    print(f"✅ Next ID: {next_id}")
    
    print("📊 Getting facebank info...")
    info = manager.get_facebank_info()
    print(f"✅ Total persons: {info['total_persons']}")
    print(f"✅ Person names: {info['person_names']}")
    print(f"✅ Facebank path: {info['facebank_path']}")
    
    print("🎉 Simple test completed successfully!")
    
except Exception as e:
    print(f"❌ Error: {str(e)}")
    import traceback
    traceback.print_exc()
