# Keycloak Configuration Template
# Copy this file to .env and fill in your actual values

# Required: Keycloak Client Configuration
KEYCLOAK_CLIENT_ID=django-face-recognition
KEYCLOAK_CLIENT_SECRET=your-keycloak-client-secret-here

# Optional: Keycloak Server Configuration (defaults provided)
KEYCLOAK_SERVER_URL=http://127.0.0.1:8080
KEYCLOAK_REALM=face-recognition

# Django Configuration
DEBUG=True
SECRET_KEY=your-django-secret-key-here

# Optional: Database Configuration (uncomment if using PostgreSQL)
# DB_NAME=face_recognition
# DB_USER=your-db-username
# DB_PASSWORD=your-db-password
# DB_HOST=localhost
# DB_PORT=5432

# Optional: Redis Configuration (for Celery)
# REDIS_URL=redis://localhost:6379