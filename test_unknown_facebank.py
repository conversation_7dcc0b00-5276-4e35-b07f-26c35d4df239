#!/usr/bin/env python3
"""
Test script for the UnknownFacebankManager functionality.
This script tests the unknown person face bank management system.
"""

import os
import sys
import django
from pathlib import Path

# Add the demo_server directory to Python path
demo_server_path = Path(__file__).parent / "demo_server"
sys.path.insert(0, str(demo_server_path))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'web_server.settings')
django.setup()

from recognition.unknown_facebank_manager import UnknownFacebankManager
from recognition.model_manager import ModelManager
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_unknown_facebank_manager():
    """Test the UnknownFacebankManager functionality"""
    print("🧪 Testing UnknownFacebankManager...")
    
    try:
        # Initialize model components
        print("📦 Loading model components...")
        model_manager = ModelManager()
        components = model_manager.get_model_components('facenet')
        
        # Initialize UnknownFacebankManager
        print("🔧 Initializing UnknownFacebankManager...")
        unknown_manager = UnknownFacebankManager(
            mtcnn=components['mtcnn'],
            model=components['model']
        )
        
        # Test getting next unknown ID
        print("\n🔢 Testing ID generation...")
        next_id = unknown_manager.get_next_unknown_id()
        print(f"   Next unknown ID: {next_id}")
        
        # Test getting facebank info
        print("\n📊 Testing facebank info...")
        info = unknown_manager.get_facebank_info()
        print(f"   Total persons: {info['total_persons']}")
        print(f"   Person names: {info['person_names']}")
        print(f"   Embeddings shape: {info['embeddings_shape']}")
        print(f"   Facebank path: {info['facebank_path']}")
        print(f"   Names path: {info['names_path']}")
        
        # Test rebuilding from directories
        print("\n🔄 Testing rebuild from directories...")
        success = unknown_manager.rebuild_unknown_facebank_from_directories()
        if success:
            print("   ✅ Rebuild successful")
            
            # Get updated info
            info = unknown_manager.get_facebank_info()
            print(f"   Updated total persons: {info['total_persons']}")
            print(f"   Updated person names: {info['person_names']}")
            print(f"   Updated embeddings shape: {info['embeddings_shape']}")
        else:
            print("   ❌ Rebuild failed")
        
        # Test next ID after rebuild
        print("\n🔢 Testing ID generation after rebuild...")
        next_id_after = unknown_manager.get_next_unknown_id()
        print(f"   Next unknown ID after rebuild: {next_id_after}")
        
        print("\n✅ UnknownFacebankManager tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_unknown_person_addition():
    """Test adding an unknown person using existing face images"""
    print("\n🧪 Testing unknown person addition...")
    
    try:
        # Initialize model components
        model_manager = ModelManager()
        components = model_manager.get_model_components('facenet')
        
        # Initialize UnknownFacebankManager
        unknown_manager = UnknownFacebankManager(
            mtcnn=components['mtcnn'],
            model=components['model']
        )
        
        # Find an existing face image to test with
        unknowns_dir = Path("demo_server/media/alert_photos/unknowns")
        test_image_path = None
        
        if unknowns_dir.exists():
            for person_dir in unknowns_dir.iterdir():
                if person_dir.is_dir() and person_dir.name.startswith("Unknown_"):
                    face_jpg = person_dir / "face.jpg"
                    if face_jpg.exists():
                        test_image_path = str(face_jpg)
                        break
        
        if test_image_path:
            print(f"   Using test image: {test_image_path}")
            
            # Get next ID
            next_id = unknown_manager.get_next_unknown_id()
            print(f"   Testing with ID: {next_id}")
            
            # Add the unknown person
            success = unknown_manager.add_unknown_person(next_id, test_image_path)
            if success:
                print("   ✅ Successfully added unknown person to facebank")
                
                # Verify it was added
                info = unknown_manager.get_facebank_info()
                print(f"   Updated total persons: {info['total_persons']}")
                print(f"   Updated person names: {info['person_names']}")
                
                # Test removal
                print(f"   Testing removal of {next_id}...")
                remove_success = unknown_manager.remove_unknown_person(next_id)
                if remove_success:
                    print("   ✅ Successfully removed unknown person from facebank")
                else:
                    print("   ❌ Failed to remove unknown person")
            else:
                print("   ❌ Failed to add unknown person to facebank")
        else:
            print("   ⚠️ No test images found, skipping addition test")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error during addition test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🚀 Starting Unknown Facebank Manager Tests")
    print("=" * 50)
    
    # Test basic functionality
    test1_success = test_unknown_facebank_manager()
    
    # Test addition/removal
    test2_success = test_unknown_person_addition()
    
    print("\n" + "=" * 50)
    if test1_success and test2_success:
        print("🎉 All tests passed!")
    else:
        print("❌ Some tests failed!")
