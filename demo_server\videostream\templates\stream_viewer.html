
{% extends "base.html" %}
{% load static %}

{% block title %}Video Stream{% endblock %}

{% block content %}

{% block base_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
<link rel="stylesheet" href="{% static 'videostream/css/stream_viewer.css' %}">
{% endblock %}

</head>
<body>
    <div class="container">
        {% block connection_header %}
        <header>
            <h1><i class="fas fa-video"></i> Video Stream</h1>
            <div class="connection-status">
                <span class="status-indicator disconnected" id="connection-indicator"></span>
                <span id="connection-text">Disconnected</span>
            </div>
        </header>
        {% endblock %}
        
        {% block main_content %}
        <div class="main-content">
            {% block camera_and_info %}
            <div>
                <div class="stream-container">
                    <img id="stream" class="stream-img" alt="Stream">
                    {% block canvas_element %}
                    <canvas id="draw-canvas" style="position: absolute; pointer-events: none; z-index: 10;"></canvas>
                    {% endblock %}
                    <div id="frame-counter"><i class="fas fa-tachometer-alt"></i> 0 FPS</div>
                    <div class="loading-spinner"></div>
                    <div class="stream-overlay">
                        <div class="stream-info">
                            <span id="stream-resolution"></span>
                        </div>
                    </div>
                </div>
                
                <div class="status-panel">
                    <h3>Stream Status</h3>
                    <div id="status">Ready to start streaming</div>
                </div>
            </div>
            {% endblock %}

            <div class="controls-panel">
                {% block stream_start_controls %}
                <div class="control-group">
                    <h3>Stream Controls</h3>
                    <input type="hidden" id="stream-url" value="{{ stream_url|safe }}">
                    <input type="hidden" id="camera-id" value="{{ camera_id|default:'' }}">
                    <div class="button-group">
                        <button class="start-btn" onclick="startStream()">
                            <i class="fas fa-play"></i> Start
                        </button>
                        <button class="stop-btn" onclick="stopStream()">
                            <i class="fas fa-stop"></i> Stop
                        </button>
                    </div>
                </div>

                <div class="control-group">
                    <h3>Zone Visibility</h3>
                    <div class="switch-container">
                        <span class="switch-label">Show Zones</span>
                        <label class="switch">
                            <input type="checkbox" id="toggle-zone-visibility" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>
                {% endblock %}

                {% block debug_info %}
                <div class="control-group">
                    <h3>Debug Information</h3>
                    <div id="debug-info">Debug information will appear here</div>
                </div>
                {% endblock %}

                {% block zone_creator %}
                {% endblock %}
            </div>
        </div>
        {% endblock %}
    </div>

    {% block stream_js %}
    <script src="{% static 'videostream/js/stream.js' %}?v=2"></script>
    {% endblock %}

    {% block page_utilities %}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const streamImg = document.getElementById('stream');
            
            // Show loading state
            streamImg.addEventListener('loadstart', function() {
                streamImg.classList.add('loading');
            });
            
            streamImg.addEventListener('load', function() {
                streamImg.classList.remove('loading');
                document.getElementById('connection-indicator').classList.replace('disconnected', 'connected');
                document.getElementById('connection-text').textContent = 'Connected';
                
                // Update resolution information
                document.getElementById('stream-resolution').textContent = 
                    `${this.naturalWidth}x${this.naturalHeight}`;
            });
            
            streamImg.addEventListener('error', function() {
                document.getElementById('connection-indicator').classList.replace('connected', 'disconnected');
                document.getElementById('connection-text').textContent = 'Disconnected';
            });
            
            // Automatically start stream if URL is provided
            const streamUrl = document.getElementById('stream-url').value;
            if (streamUrl) {
                startStream();
            }
            
            // Zone filter activation
            setTimeout(function() {
                const cameraId = document.getElementById('camera-id').value;
                activateZoneFilter(cameraId);
            }, 1000);        });
    </script>
    
    <script src="{% static 'videostream/js/stream.js' %}?v=7"></script>
    {% endblock %}

    {% block zone_creator_js %}
    {% endblock %}
    {% block zone_visibility_js %}
    <script src="{% static 'videostream/js/canvas_manager.js' %}"></script>
    <script src="{% static 'videostream/js/polygon.js' %}"></script>
    <script>
        // Zone visibility control for stream viewer
        document.addEventListener('DOMContentLoaded', function() {
            const canvas = document.getElementById("draw-canvas");
            const video = document.getElementById("stream");
            const toggleZoneVisibility = document.getElementById("toggle-zone-visibility");

            if (!canvas || !video || !toggleZoneVisibility) {
                console.log("Zone visibility elements not found, skipping zone display setup");
                return;
            }

            const canvasManager = new CanvasManager(canvas);
            let polygons = [];
            let zonesLoaded = false;

            // Function to update canvas position and size
            function updateCanvasPosition() {
                if (video.complete && video.naturalWidth !== 0) {
                    canvasManager.resizeToMatch(video);
                    if (zonesLoaded && toggleZoneVisibility.checked) {
                        canvasManager.draw();
                    }
                }
            }

            // Add event listeners for video events
            video.addEventListener('load', updateCanvasPosition);
            video.addEventListener('loadeddata', updateCanvasPosition);
            video.addEventListener('resize', updateCanvasPosition);

            // Toggle zone visibility
            toggleZoneVisibility.addEventListener("change", () => {
                const isVisible = toggleZoneVisibility.checked;
                canvasManager.setPolygons(isVisible ? polygons : []);
                canvasManager.draw();
                console.log("Zone visibility toggled:", isVisible);
            });

            // Fetch zones from server
            async function fetchZones() {
                const cameraId = document.getElementById("camera-id").value;
                if (!cameraId) {
                    console.log("No camera ID found, skipping zone fetch");
                    return;
                }

                try {
                    const response = await fetch(`/videostream/get_zones/?camera_id=${cameraId}`);
                    const result = await response.json();
                    if (response.ok) {
                        const videoWidth = video.clientWidth;
                        const videoHeight = video.clientHeight;

                        polygons = result.zones.map(zone => {
                            const polygon = new Polygon(zone.color);
                            polygon.name = zone.name;

                            // Scale points back to video dimensions
                            polygon.points = zone.points.map(point => ({
                                x: point.x * videoWidth,
                                y: point.y * videoHeight
                            }));

                            polygon.complete = true;
                            return polygon;
                        });

                        zonesLoaded = true;

                        // Only show zones if toggle is checked
                        if (toggleZoneVisibility.checked) {
                            canvasManager.setPolygons(polygons);
                            canvasManager.draw();
                        }

                        console.log(`Loaded ${polygons.length} zones for display`);
                    } else {
                        console.error(`Error fetching zones: ${result.error}`);
                    }
                } catch (error) {
                    console.error("Error fetching zones:", error);
                }
            }

            // Wait for video to load before fetching zones
            function initZoneDisplay() {
                if (video.complete && video.naturalWidth !== 0) {
                    updateCanvasPosition();
                    fetchZones();
                } else {
                    // Wait a bit more for video to load
                    setTimeout(initZoneDisplay, 500);
                }
            }

            // Start zone display initialization after a delay
            setTimeout(initZoneDisplay, 2000);
        });
    </script>
    {% endblock %}

{% endblock %}